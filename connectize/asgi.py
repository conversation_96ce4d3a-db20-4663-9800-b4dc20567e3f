import os

import django
from channels.auth import AuthMiddlewareStack
from channels.routing import Protocol<PERSON><PERSON><PERSON><PERSON><PERSON>, URLRouter
from django.core.asgi import get_asgi_application
from django.urls import re_path

import chat.routing
import notification.routing
from connectize.websocket.unified_consumer import UnifiedConsumer
from notification.middleware import JWTAuthMiddleware

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'connectize.settings.production')
django.setup()

# Unified WebSocket routing
unified_websocket_urlpatterns = [
    re_path(r"ws/unified/$", UnifiedConsumer.as_asgi()),
]

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": JWTAuthMiddleware(
            AuthMiddlewareStack(
                URLRouter(
                    unified_websocket_urlpatterns +
                    # Legacy routing for backward compatibility during transition
                    chat.routing.websocket_urlpatterns +
                    notification.routing.websocket_urlpatterns
                )
        )
    ),
})
