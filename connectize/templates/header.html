{% load wagtailimages_tags %}{% load static menus_tags %}{% get_menu "waitlist-menu" as menu %}
<div class="row navigation-header">
    <div class="col justify-content-start logo">
        <a href="/"><img src="/static/images/connectizelogo (2).png" alt="Connectize logo"></a>
    </div>
    <div class="col nav-menus">
        <ul class="nav justify-content-end align-items-center">
            {% for item in menu.menu_items.all %}
            <li class="nav-item">
                <a class="nav-link active" aria-current="page" href="{{item.link}}" title="{{item.title}}" {% if item.open_in_new_tab %} target="_blank" {% endif %}>{{item.title}}</a>
            </li>
            {% endfor %}
            <li class="nav-item">
                <a class="nav-link btn btn-primary join-waitlist-btn" role="button" href="/#join-waitlist">Join waitlist</a>
            </li>
        </ul>
    </div>
    <button class="btn btn-primary ham-burger-menu" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasTop" aria-controls="offcanvasTop">
        <svg xmlns="http://www.w3.org/2000/svg" width="56" height="57" viewBox="0 0 56 57" fill="none">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.3999 14.25C8.3999 12.676 9.6535 11.4 11.1999 11.4H44.7999C46.3463 11.4 47.5999 12.676 47.5999 14.25C47.5999 15.824 46.3463 17.1 44.7999 17.1H11.1999C9.6535 17.1 8.3999 15.824 8.3999 14.25Z"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.3999 28.5C8.3999 26.926 9.6535 25.65 11.1999 25.65H44.7999C46.3463 25.65 47.5999 26.926 47.5999 28.5C47.5999 30.074 46.3463 31.35 44.7999 31.35H11.1999C9.6535 31.35 8.3999 30.074 8.3999 28.5Z"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M25.1999 42.75C25.1999 41.176 26.4535 39.9 27.9999 39.9H44.7999C46.3463 39.9 47.5999 41.176 47.5999 42.75C47.5999 44.324 46.3463 45.6 44.7999 45.6H27.9999C26.4535 45.6 25.1999 44.324 25.1999 42.75Z"/>
        </svg>
    </button>
    <div class="offcanvas offcanvas-top" tabindex="-1" id="offcanvasTop" aria-labelledby="offcanvasTopLabel">
        <div class="offcanvas-header">
            <div class="col justify-content-start logo" id="offcanvasTopLabel" style="color:black;">
                <a href="/"><img src="/static/images/connectizelogo (2).png" alt="Connectize logo"></a>
            </div>
            <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <ul class="nav align-items-center">
                {% for item in menu.menu_items.all %}
                <li class="nav-item">
                    <a class="nav-link active" aria-current="page" href="{{item.link}}" title="{{item.title}}" {% if item.open_in_new_tab %} target="_blank" {% endif %}>{{item.title}}</a>
                </li>
                {% endfor %}
                <li class="nav-item">
                    <a class="nav-link btn btn-primary join-waitlist-btn" role="button" href="/#join-waitlist">Join waitlist</a>
                </li>
            </ul>
        </div>
    </div>
</div>