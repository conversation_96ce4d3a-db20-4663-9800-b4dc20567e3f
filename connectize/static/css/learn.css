:root {
    --primary-orange: #F1C644;
    --white: #F9F9F9;
    --black: #000000;
    --primary-blck: #1B1B1B;
    --font-drk-600: #242424;
}
.nav-link {
    color: #ffffff;
}
.ham-burger-menu svg{
    fill: #FFF;
}
.join-waitlist-btn {
    background: white;
    color: black;
}
.learn_more_welcome-text{
    margin: 30px auto 30px 30px;
}
.learn_more_welcome-text h1 {
    color: white;
    font-size: 84px;
    font-weight: 700;
    line-height: 85px;
    text-align: left;
    max-width: 884px;
    margin-bottom: 0;
}
/* .learn_more_welcome-text span {
    color: var(--primary-orange);
} */

.learn_more_welcome-text p {
    color: white;
    font-size: 24px;
    font-weight: 500;
    line-height: 30px;
    text-align: left;
    max-width: 770px;
    margin-top: 23px;
}

.learn-more-header {
    /* height: 293px; */
    border-radius: 10px;
    display: flex;
    align-items: center;
}

.learn-more-header h2 {
    color: #FFF !important;
    font-size: 74px;
    font-weight: 700;
    text-transform: uppercase;
}

.learn-more-header button {
    background: none;
    color: #FFF !important;
    font-size: 74px;
    font-weight: 700;
    text-transform: uppercase;
}

.accordion-item h2 button {
    color: #fff !important;
    font-size: 74px !important;
    font-weight: 700 !important;
}

.accordion-button:not(.collapsed) {
    color: #fff !important;
}

.accordion-button::after {
    flex-shrink: 0;
    width: 82px;
    height: 82px;
    margin-left: auto;
    content: "";
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="82" height="82" viewBox="0 0 82 82" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M40.9998 12.3C43.2642 12.3 45.0998 14.1356 45.0998 16.4V36.9H65.5998C67.8642 36.9 69.6998 38.7356 69.6998 41C69.6998 43.2644 67.8642 45.1 65.5998 45.1H45.0998V65.6C45.0998 67.8644 43.2642 69.7 40.9998 69.7C38.7354 69.7 36.8998 67.8644 36.8998 65.6V45.1H16.3998C14.1354 45.1 12.2998 43.2644 12.2998 41C12.2998 38.7356 14.1354 36.9 16.3998 36.9L36.8998 36.9V16.4C36.8998 14.1356 38.7354 12.3 40.9998 12.3Z" fill="white"/></svg>');
    background-repeat: no-repeat;
    background-size: 82px;
    transition: transform .2s ease-in-out;
}

.accordion-button:not(.collapsed)::after {
    background-image: url('data: image/svg+xml, <svg xmlns="http://www.w3.org/2000/svg" width="78" height="78" viewBox="0 0 78 78" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M16.7424 16.7423C18.2654 15.2192 20.7348 15.2192 22.2578 16.7423L39.0001 33.4845L55.7424 16.7423C57.2654 15.2192 59.7348 15.2192 61.2578 16.7423C62.7809 18.2653 62.7809 20.7346 61.2578 22.2577L44.5155 39L61.2578 55.7423C62.7809 57.2653 62.7809 59.7347 61.2578 61.2577C59.7348 62.7807 57.2654 62.7807 55.7424 61.2577L39.0001 44.5154L22.2578 61.2577C20.7348 62.7807 18.2654 62.7807 16.7424 61.2577C15.2193 59.7347 15.2193 57.2653 16.7424 55.7423L33.4847 39L16.7424 22.2577C15.2193 20.7346 15.2193 18.2653 16.7424 16.7423Z" fill="white"/></svg>');
}

.learn-more-container {
    padding: 10px 0 80px 0;
}

.learn-more-container .accordion-item {
    margin-top: 42px;
}

.features-accordion {
    background: #373737;
}

.features-accordion .service-feature-card svg {
    background: #FFF;
    border-radius: 10px;
}

.feature-card-2 *,
.feature-card-3 *,
.feature-card-4 * {
    color: #FFF !important;
}

.service-feature-card {
    display: flex;
    align-items: start;
    flex-direction: column;
}

.service-feature-card p {
    text-align: left;
}

.service-feature-card svg {
    margin-left: 0;
    margin-right: auto;
}

.purpose-card {
    border-radius: 16px;
    border: 2px solid #FFF;
    padding: 40px;
}

.about-us-body {
    padding: 40px 60px;
}

.about-us-body p {
    text-align: left;
    font-size: 19px;
    font-weight: 400;
    color: #f9f9f9;
}

.about-us-body h2 {
    color: #FFF;
    font-size: 55px;
    font-weight: 700;
    margin-bottom: 33px;
}

.about-us-body h3 {
    color: #FFF;
    font-size: 28px;
    font-weight: 700;
}

.about-first-section p {
    max-width: 930px;
}

.about-first-section {
    margin-bottom: 40px;
}

.purpose-card {
    margin-bottom: 29px;
}

.purpose-card {
    margin-bottom: 29px;
}

.our-values-body .card {
    padding: 40px;
    border-radius: 20px;
    border: 1px solid #F9F9F9;
    background: rgba(255, 255, 255, 0.33);
    box-shadow: 12px 24px 44px 0px rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(5px);
}

.our-values-body .card p {
    text-align: left;
}

.our-values-body .card h5 {
    color: #F9F9F9;
    font-size: 25px;
    font-weight: 700;
}

.our-values-body .card img {
    border-radius: 20px;
    width: 100%;
    max-height: 200px;
}
.our-values-body p{
    color: #F9F9F9;
}
@media screen and (max-width: 750px) {
    .learn_more_welcome-text h1 {
        font-size: 60px;
    }
}

@media screen and (max-width: 560px) {
    .learn_more_welcome-text h1 {
        font-size: 40px;
        line-height: 1;
    }
    .learn_more_welcome-text p {
        font-size: 18px;
    }
    .service-feature-card {
        width: 100%;
        height: fit-content;
        padding: 30px;
        min-width: 280px;
    }
    .learn-more-container .service-feature-card h2 {
        color: var(--primary-blck);
        text-align: left;
        font-size: 24px;
        font-weight: 700;
        backdrop-filter: none;
    }
    .about-us-body {
        padding: 40px 10px;
    }
    .purpose-card {
        padding: 40px 10px;
    }
    .our-values-body .card {
        padding: 40px 10px;
    }
    .learn-more-header {
        height: 100px !important;
    }
}

@media screen and (max-width: 450px) {
    .accordion-item h2 button {
        font-size: 40px !important;
    }
}