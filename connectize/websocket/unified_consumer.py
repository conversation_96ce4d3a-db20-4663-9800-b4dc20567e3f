"""
Unified WebSocket Consumer for handling all real-time communication.

This consumer consolidates functionality from:
- AllChatsConsumer (general chat notifications)
- ChatConsumer (room-specific messages)
- GroupChatConsumer (group chat functionality)
- NotificationConsumer (system notifications)
- AppSocketConsumer (general app events)
"""

import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.utils.timezone import now
from core.channels_socket.clients import register_client, unregister_client
from notification.models import Notification
from chat.models import Message

logger = logging.getLogger(__name__)


class UnifiedConsumer(AsyncWebsocketConsumer):
    """
    Single WebSocket consumer handling all real-time communication for a user.
    Provides feature parity with all existing consumers while using one connection.
    """

    async def connect(self):
        """Handle WebSocket connection with authentication."""
        if not self.scope["user"].is_authenticated:
            logger.warning("Unauthenticated user attempted WebSocket connection")
            await self.close()
            return

        self.user = self.scope["user"]
        self.user_id = str(self.user.id)

        # Single global group per user for all communications
        self.global_group = f"unified_user_{self.user_id}"

        # Add to global user group
        await self.channel_layer.group_add(self.global_group, self.channel_name)
        await self.accept()

        # Register client for presence tracking (from AppSocketConsumer)
        register_client(self.user_id, self.channel_name)

        logger.info(f"User {self.user_id} connected to unified WebSocket")

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection."""
        if hasattr(self, 'global_group'):
            await self.channel_layer.group_discard(self.global_group, self.channel_name)

        if hasattr(self, 'user_id'):
            unregister_client(self.user_id, self.channel_name)
            logger.info(f"User {self.user_id} disconnected from unified WebSocket")

    async def receive(self, text_data):
        """Handle incoming messages from client with routing based on event type."""
        try:
            data = json.loads(text_data)
            event_type = data.get("eventType")

            logger.debug(f"Received message from user {self.user_id}: {event_type}")

            # Route based on event type
            if event_type == "chat_message":
                await self.handle_chat_message(data)
            elif event_type == "mark_as_read":
                await self.handle_mark_as_read(data)
            elif event_type == "mark_all_as_read":
                await self.handle_mark_all_as_read(data)
            elif event_type == "notification_action":
                await self.handle_notification_action(data)
            elif event_type == "group_message":
                await self.handle_group_message(data)
            elif event_type == "app_event":
                await self.handle_app_event(data)
            else:
                logger.warning(f"Unknown event type: {event_type}")

        except json.JSONDecodeError:
            logger.error(f"Invalid JSON received from user {self.user_id}")
        except Exception as e:
            logger.error(f"Error handling message from user {self.user_id}: {str(e)}")

    async def handle_chat_message(self, data):
        """Handle chat message events (from ChatConsumer functionality)."""
        room_name = data.get("roomName")
        message_data = data.get("message")

        if not room_name or not message_data:
            return

        # Broadcast to room participants
        await self.channel_layer.group_send(
            f"unified_room_{room_name}",
            {
                "type": "unified_message",
                "event_type": "chat_message",
                "event_name": "message_received",
                "room_id": room_name,
                "payload": message_data,
            }
        )

    async def handle_mark_as_read(self, data):
        """Handle mark as read events (from ChatConsumer functionality)."""
        message_id = data.get("messageId")
        user_id = data.get("userId")

        if message_id and user_id:
            await self.mark_message_as_read(message_id, user_id)

    async def handle_mark_all_as_read(self, data):
        """Handle mark all as read events."""
        room_name = data.get("roomName")
        if room_name:
            # This would trigger the bulk read logic
            pass

    async def handle_notification_action(self, data):
        """Handle notification actions (from NotificationConsumer functionality)."""
        command = data.get("command")
        notification_id = data.get("notificationId")

        if command == "mark_as_read" and notification_id:
            await self.mark_notification_as_read(notification_id)
        elif command == "delete_notification" and notification_id:
            await self.mark_notification_as_deleted(notification_id)
        elif command == "delete_all_notifications":
            await self.mark_all_notifications_as_deleted()

    async def handle_group_message(self, data):
        """Handle group message events (from GroupChatConsumer functionality)."""
        room_name = data.get("roomName")
        message_data = data.get("message")
        sender = data.get("sender")

        if not room_name or not message_data:
            return

        # Broadcast to group participants
        await self.channel_layer.group_send(
            f"unified_group_{room_name}",
            {
                "type": "unified_message",
                "event_type": "group_message",
                "event_name": "group_message_received",
                "room_id": room_name,
                "payload": {"message": message_data, "sender": sender},
            }
        )

    async def handle_app_event(self, data):
        """Handle general app events (from AppSocketConsumer functionality)."""
        event = data.get("event")
        message = data.get("message")

        if event == "send" and message:
            await self.channel_layer.group_send(
                "unified_chatroom",
                {
                    "type": "unified_message",
                    "event_type": "app_event",
                    "event_name": "new_message",
                    "payload": {
                        "userId": self.user_id,
                        "message": message,
                    }
                }
            )

    # Unified message handler for all event types
    async def unified_message(self, event):
        """
        Unified handler for all message types.
        Sends standardized message format to client for proper routing.
        """
        message = {
            "eventName": event.get("event_name", "unknown_event"),
            "eventType": event.get("event_type"),
            "roomId": event.get("room_id"),
            "payload": event.get("payload")
        }

        await self.send(text_data=json.dumps(message))

    # Database operations (async versions of existing functionality)
    @database_sync_to_async
    def mark_message_as_read(self, message_id, user_id):
        """Mark a chat message as read (from ChatConsumer)."""
        message = Message.objects.filter(id=message_id, user_id=user_id).first()
        if message:
            message.read_at = now()
            message.save()

    @database_sync_to_async
    def mark_notification_as_read(self, notification_id):
        """Mark a notification as read (from NotificationConsumer)."""
        notification = Notification.objects.filter(
            id=notification_id,
            user=self.user
        ).first()
        if notification:
            notification.is_read = now()
            notification.save()

    @database_sync_to_async
    def mark_notification_as_deleted(self, notification_id):
        """Mark a notification as deleted (from NotificationConsumer)."""
        notification = Notification.objects.filter(
            id=notification_id,
            user=self.user
        ).first()
        if notification:
            notification.deleted_at = now()
            notification.save()

    @database_sync_to_async
    def mark_all_notifications_as_deleted(self):
        """Mark all notifications as deleted (from NotificationConsumer)."""
        notifications = Notification.objects.filter(
            user=self.user,
            deleted_at__isnull=True
        )
        for notification in notifications:
            notification.deleted_at = now()
            notification.save()
