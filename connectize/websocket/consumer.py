import json

from channels.generic.websocket import AsyncWebsocketConsumer


class BaseWebSocketConsumer(AsyncWebsocketConsumer):
    """A reusable base WebSocket consumer for handling connections, groups, and messages."""

    async def connect(self):
        """Handles WebSocket connection authentication."""
        if self.scope["user"].is_authenticated:
            self.user = self.scope["user"]
            self.group_name = self.get_group_name()
            
            if self.channel_layer is None:
                print("🔴 ERROR: channel_layer is None. Check ASGI settings!")
            
            await self.channel_layer.group_add(self.group_name, self.channel_name)
            await self.accept()
        else:
            await self.close()

    async def disconnect(self, code):
        """Handles WebSocket disconnection."""
        if hasattr(self, "group_name"):
            await self.channel_layer.group_discard(self.group_name, self.channel_name)
        print(f"Client disconnected with code: {code}")

    async def receive(self, text_data):
        """Handles received messages from the client."""
        data = json.loads(text_data)
        await self.handle_message(data)

    async def send_message(self, message, event_name=None):
        """
        Sends a message to the WebSocket with standardized format.

        Args:
            message: The message payload to send
            event_name: Optional event name. If not provided, will be inferred from the calling method.
        """
        # If event_name is not provided, try to infer it from the calling method
        if event_name is None:
            import inspect
            frame = inspect.currentframe()
            try:
                # Get the calling method name
                caller_name = frame.f_back.f_code.co_name
                event_name = self._map_method_to_event_name(caller_name)
            finally:
                del frame

        # Create standardized message format
        standardized_message = {
            "eventName": event_name or "unknown_event",
            "payload": message
        }

        await self.send(text_data=json.dumps(standardized_message))

    async def send_group_message(self, event):
        """Sends a message to a WebSocket group with standardized format."""
        event_name = self._map_method_to_event_name(event.get("type", "unknown"))
        await self.send_message(event["message"], event_name)

    def _map_method_to_event_name(self, method_name):
        """
        Maps consumer method names to standardized event names.

        Args:
            method_name: The name of the consumer method

        Returns:
            str: The standardized event name
        """
        event_mapping = {
            "send_notification": "notification_received",
            "chat_message": "message_received",
            "read_receipt": "read_receipt_received",
            "send_group_message": "group_message_received",
        }

        return event_mapping.get(method_name, f"{method_name}_event")

    async def handle_message(self, data):
        """Handles incoming messages (to be overridden by child classes)."""
        pass

    def get_group_name(self):
        """Returns the group name (to be overridden by child classes)."""
        raise NotImplementedError("Subclasses must implement `get_group_name` method.")
