"""
Unified WebSocket Service for broadcasting messages to the unified consumer.

This service replaces the existing WebSocketService and provides
unified message broadcasting for all real-time features.
"""

import logging
from typing import List, Optional, Dict, Any
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer

logger = logging.getLogger(__name__)


class UnifiedWebSocketService:
    """
    Service for broadcasting messages to users via the unified WebSocket consumer.
    Replaces the existing WebSocketService with unified message handling.
    """

    @staticmethod
    def send_to_user(user_id: int, event_type: str, payload: Dict[str, Any], 
                     event_name: Optional[str] = None, room_id: Optional[str] = None):
        """
        Send a message to a specific user via their unified WebSocket connection.
        
        Args:
            user_id: Target user ID
            event_type: Type of event (chat_message, notification, read_receipt, etc.)
            payload: Message payload data
            event_name: Optional event name (auto-generated if not provided)
            room_id: Optional room ID for room-specific messages
        """
        if not event_name:
            event_name = f"{event_type}_received"
            
        channel_layer = get_channel_layer()
        
        try:
            async_to_sync(channel_layer.group_send)(
                f"unified_user_{user_id}",
                {
                    "type": "unified_message",
                    "event_type": event_type,
                    "event_name": event_name,
                    "room_id": room_id,
                    "payload": payload
                }
            )
            logger.debug(f"Sent {event_type} to user {user_id}")
        except Exception as e:
            logger.error(f"Failed to send message to user {user_id}: {str(e)}")

    @staticmethod
    def send_to_users(user_ids: List[int], event_type: str, payload: Dict[str, Any],
                      event_name: Optional[str] = None, room_id: Optional[str] = None):
        """
        Send a message to multiple users via their unified WebSocket connections.
        
        Args:
            user_ids: List of target user IDs
            event_type: Type of event
            payload: Message payload data
            event_name: Optional event name
            room_id: Optional room ID for room-specific messages
        """
        for user_id in user_ids:
            UnifiedWebSocketService.send_to_user(
                user_id=user_id,
                event_type=event_type,
                payload=payload,
                event_name=event_name,
                room_id=room_id
            )

    @staticmethod
    def broadcast_chat_message(message_data: Dict[str, Any], sender_id: int, 
                              recipient_id: int, room_name: str):
        """
        Broadcast a chat message to participants.
        Maintains compatibility with existing chat message format.
        
        Args:
            message_data: Complete message data including images, content, etc.
            sender_id: ID of message sender
            recipient_id: ID of message recipient
            room_name: Chat room identifier
        """
        # Send to both participants
        participants = [sender_id, recipient_id]
        
        UnifiedWebSocketService.send_to_users(
            user_ids=participants,
            event_type="chat_message",
            payload=message_data,
            event_name="message_received",
            room_id=room_name
        )
        
        # Also send to recipient's general chat notifications
        UnifiedWebSocketService.send_to_user(
            user_id=recipient_id,
            event_type="chat_notification",
            payload=message_data,
            event_name="message_received",
            room_id=room_name
        )

    @staticmethod
    def broadcast_group_message(message_data: Dict[str, Any], group_members: List[int], 
                               room_name: str):
        """
        Broadcast a group message to all group members.
        
        Args:
            message_data: Complete message data
            group_members: List of group member user IDs
            room_name: Group room identifier
        """
        UnifiedWebSocketService.send_to_users(
            user_ids=group_members,
            event_type="group_message",
            payload=message_data,
            event_name="group_message_received",
            room_id=room_name
        )

    @staticmethod
    def send_notification(notification_data: Dict[str, Any], recipient_id: int):
        """
        Send a system notification to a user.
        Maintains compatibility with existing notification format.
        
        Args:
            notification_data: Complete notification data
            recipient_id: Target user ID
        """
        UnifiedWebSocketService.send_to_user(
            user_id=recipient_id,
            event_type="notification",
            payload=notification_data,
            event_name="notification_received"
        )

    @staticmethod
    def send_read_receipt(receipt_data: Dict[str, Any], recipient_id: int, 
                         room_name: Optional[str] = None):
        """
        Send a read receipt notification.
        
        Args:
            receipt_data: Read receipt data
            recipient_id: Target user ID
            room_name: Optional room name for room-specific receipts
        """
        UnifiedWebSocketService.send_to_user(
            user_id=recipient_id,
            event_type="read_receipt",
            payload=receipt_data,
            event_name="read_receipt_received",
            room_id=room_name
        )

    @staticmethod
    def send_app_event(event_data: Dict[str, Any], user_ids: List[int]):
        """
        Send general app events to users.
        
        Args:
            event_data: Event data
            user_ids: List of target user IDs
        """
        UnifiedWebSocketService.send_to_users(
            user_ids=user_ids,
            event_type="app_event",
            payload=event_data,
            event_name="app_event_received"
        )

    # Compatibility methods to maintain existing API
    @staticmethod
    def send_websocket_notification(notification=None, recipient=None, ws_message=None, 
                                   message_type="send_notification", channel_group_name=None):
        """
        Compatibility method for existing WebSocketService.send_websocket_notification calls.
        Translates old API to new unified service.
        """
        # Determine recipient
        if recipient:
            recipient_id = recipient.id if hasattr(recipient, 'id') else recipient
        elif notification and hasattr(notification, 'user'):
            recipient_id = notification.user.id
        else:
            logger.error("No recipient specified for WebSocket notification")
            return

        # Prepare message payload
        if ws_message:
            payload = ws_message
        elif notification:
            payload = {
                "id": notification.id,
                "title": notification.title,
                "message": notification.message,
                "link": notification.link,
                "timestamp": str(notification.timestamp),
                "user": recipient_id,
                "sender": notification.sender.id if notification.sender else None,
                "notification_type": notification.notification_type,
                "extra_data": notification.extra_data,
                "is_read": None
            }
        else:
            logger.error("No message data provided for WebSocket notification")
            return

        # Extract room ID from channel group name if provided
        room_id = None
        if channel_group_name:
            if channel_group_name.startswith("chat_"):
                room_id = channel_group_name.replace("chat_", "")
            elif channel_group_name.startswith("chat_user_"):
                room_id = None  # User-specific notification
            elif channel_group_name.startswith("user_"):
                room_id = None  # User-specific notification

        # Map message types to event types
        event_type_mapping = {
            "send_notification": "notification",
            "chat_message": "chat_message",
            "read_receipt": "read_receipt",
            "group_message": "group_message"
        }
        
        event_type = event_type_mapping.get(message_type, message_type)
        
        # Send via unified service
        UnifiedWebSocketService.send_to_user(
            user_id=recipient_id,
            event_type=event_type,
            payload=payload,
            room_id=room_id
        )

        logger.debug(f"Sent {message_type} notification to user {recipient_id} via unified service")
