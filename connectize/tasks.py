from celery import shared_task
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth import get_user_model

User = get_user_model()

@shared_task
def delete_old_deactivated_users():
    threshold_date = timezone.now() - timedelta(days=30)
    deleted_count, _ = User.objects.filter(deactivated_at__lte=threshold_date).delete()
    return f"Deleted {deleted_count} users."
