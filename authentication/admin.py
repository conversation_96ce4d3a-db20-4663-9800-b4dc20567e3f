from django.contrib import admin

from .models import User, Gender
from api.models.company_model import Company, CompanyCategory, CompanySize
from api.models.product_model import Product, ProductCategory, ProductImage
from api.models.post_model import Post, PostImage
from api.models.service_model import Service, ServiceCategory, ServiceImage
from api.models.document_model import Document, DocumentType
from api.models.comment_model import Comment, FollowingRelationships, \
    Review, Like, Reply
from api.models.representative import Representative, RepresentativeCategory
from chat.models import Message

# Register your models here.

admin.site.register(User)
admin.site.register(Gender)
admin.site.register(CompanyCategory)
admin.site.register(CompanySize)
admin.site.register(Company)
admin.site.register(Product)
admin.site.register(ProductCategory)
admin.site.register(ProductImage)
admin.site.register(Post)
admin.site.register(PostImage)
admin.site.register(Service)
admin.site.register(ServiceImage)
admin.site.register(ServiceCategory)
admin.site.register(Document)
admin.site.register(DocumentType)
admin.site.register(Comment)
admin.site.register(Like)
admin.site.register(FollowingRelationships)
admin.site.register(Reply)
admin.site.register(Review)
admin.site.register(Representative)
admin.site.register(RepresentativeCategory)
admin.site.register(Message)