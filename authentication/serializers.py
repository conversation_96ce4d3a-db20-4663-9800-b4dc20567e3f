from rest_framework import serializers
from .models import User

class UserSerializer(serializers.ModelSerializer):
    full_name = serializers.ReadOnlyField()
    gender_name = serializers.CharField(source="gender.name", read_only=True)  
    
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'full_name', 'gender', 'gender_name',
            'date_of_birth', 'bio', 'role', 'company', 'phone_number', 'verified',
            'date_joined', 'last_login', 'country', 'region', 'city', 'address', 'avatar',
            'is_first_time_user', 'is_active', 'is_admin', 'is_staff', 'accept_terms'
        ]
        read_only_fields = ['date_joined', 'last_login', 'verified']
