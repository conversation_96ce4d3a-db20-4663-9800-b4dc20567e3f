from django.urls import path
from rest_framework_simplejwt.views import (
        TokenObtainPairView,
        TokenRefreshView,
        TokenVerifyView,
    )
from .views import (
        LogoutView, ChangePasswordView,
        password_reset_confirm,
        request_password_reset,
        UserRegistrationView,
        UserSignInView,
        resend_verification_email, verify_account, 
        DeactivateAccountView, ReactivateAccountView,RequestAccountReactivation
    )

urlpatterns = [
    path('registration/', UserRegistrationView.as_view(), name="user_registration"),
    path("login/", UserSignInView.as_view(), name='user_login'),
    path("logout/", LogoutView.as_view(), name='user_logout'),
    
    path("deactivate-account/", DeactivateAccountView.as_view(), name='deactivate_account'),
    path("request-account-reactivation/", RequestAccountReactivation.as_view(), name='request_reactivate_account'),
    path("reactivate-account/<uidb64>/<token>/", ReactivateAccountView.as_view(), name='reactivate_account'),
    
    # Tokens
    path("token/", TokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("refresh-token/", TokenRefreshView.as_view(), name="token_refresh"),
    path("verify-token/", TokenVerifyView.as_view(), name="token_verify"),
    
    path('resend_verification_email/', resend_verification_email, name='resend_verification_email'),
    path('verify-account/<uidb64>/<token>/', verify_account, name='verify_account'),
    
    path('change-password/', ChangePasswordView.as_view(), name='change_password'),
    path('reset-password/', request_password_reset, name='reset_password'),
    path('confirm-reset-password/<uidb64>/<token>/', password_reset_confirm, name='confirm_reset_password'),
]
