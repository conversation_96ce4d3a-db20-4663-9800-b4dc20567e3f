from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = "Deletes users who have been deactivated for more than 30 days"

    def handle(self, *args, **kwargs):
        threshold_date = timezone.now() - timedelta(days=30)
        deleted_count, _ = User.objects.filter(deactivated_at__lte=threshold_date).delete()
        self.stdout.write(self.style.SUCCESS(f"Deleted {deleted_count} users."))
