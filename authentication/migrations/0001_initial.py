# Generated by Django 4.2.15 on 2025-06-30 21:41

from django.db import migrations, models
import django.db.models.deletion
import wagtail.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="Gender",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("type", models.CharField(blank=True, max_length=500, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "is_superuser",
                    models.Bo<PERSON>anField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        max_length=255, unique=True, verbose_name="email"
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="First Name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="Last Name"
                    ),
                ),
                ("date_of_birth", models.DateField(blank=True, null=True)),
                ("bio", wagtail.fields.RichTextField(blank=True, null=True)),
                ("role", models.CharField(blank=True, max_length=500, null=True)),
                ("company", models.CharField(blank=True, max_length=500, null=True)),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("verified", models.BooleanField(default=False)),
                ("accept_terms", models.BooleanField(default=False)),
                (
                    "date_joined",
                    models.DateTimeField(
                        auto_now_add=True, null=True, verbose_name="date joined"
                    ),
                ),
                (
                    "last_login",
                    models.DateTimeField(
                        auto_now=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "country",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="country"
                    ),
                ),
                (
                    "region",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="region"
                    ),
                ),
                (
                    "city",
                    models.CharField(
                        blank=True, max_length=255, null=True, verbose_name="country"
                    ),
                ),
                ("address", models.CharField(blank=True, max_length=255, null=True)),
                ("avatar", models.ImageField(blank=True, null=True, upload_to="")),
                ("is_first_time_user", models.BooleanField(default=True)),
                (
                    "user_type",
                    models.CharField(
                        choices=[
                            ("company", "Company"),
                            ("user_viewer", "User Viewer"),
                            ("super_user", "Super User"),
                        ],
                        default="user_viewer",
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=False)),
                ("is_admin", models.BooleanField(default=False)),
                ("is_staff", models.BooleanField(default=False)),
                (
                    "deactivated_at",
                    models.DateTimeField(null=True, verbose_name="Account Deactivated"),
                ),
                (
                    "gender",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to="authentication.gender",
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "verbose_name": "User",
                "verbose_name_plural": "Users",
                "ordering": ["-date_joined"],
            },
        ),
    ]
