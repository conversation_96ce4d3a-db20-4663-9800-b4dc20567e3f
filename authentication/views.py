from django.contrib.auth import authenticate
from django.utils.http import urlsafe_base64_decode
from django.utils.encoding import force_str

from rest_framework import status
from rest_framework.decorators import (
        api_view, permission_classes, throttle_classes
    )
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated, AllowAny

from rest_framework_simplejwt.tokens import RefreshToken

from responselib.helpers import apiResponse
from .models import User
from .forms import UserRegisterForm

from rest_framework.throttling import UserRateThrottle
from mailing.views import (
    send_reactivation_notification_email,
    send_verification_email, token_generator,
    send_reset_password_email, send_deactivation_email_to_admin,
    send_reactivation_email, send_password_change_email)
from django.utils.timezone import now
from django.db import transaction
# from django.conf import settings

deactivation_message = "Your account has been deactivated. Please contact support to reactivate your account."
throttle_message = "Too many request. Please try again after an hour."

CUSTOM_DEACTIVATED_ACCOUNT_STATUS = 417
CUSTOM_REACTIVATED_ACCOUNT_STATUS = 418

FREE_EMAIL_PROVIDERS = {
    "gmail.com", "yahoo.com", "outlook.com", "hotmail.com", "icloud.com",
    "live.com", "aol.com", "zoho.com", "yandex.com", "protonmail.com",
    "gmx.com", "fastmail.com", "tutanota.com", "mail.com", "inbox.com",
    "me.com", "mac.com", "msn.com", "mail.ru", "rediffmail.com",
    "naver.com", "qq.com", "163.com", "126.com", "sina.com",
    "rocketmail.com", "yahoo.co.uk", "yahoo.co.in", "btinternet.com",
    "web.de", "laposte.net", "telus.net", "earthlink.net", "juno.com",
    "att.net", "comcast.net", "cox.net", "verizon.net",
    "charter.net", "rogers.com", "shaw.ca", "sky.com",
    "bigpond.com", "optonline.net", "ntlworld.com",
    "blueyonder.co.uk", "sympatico.ca", "windstream.net",
    "frontiernet.net", "bellsouth.net", "embarqmail.com",
    "centurylink.net", "aim.com", "myway.com", "lycos.com",
    "mailnesia.com", "zoho.eu", "outlook.co.uk", "mailbox.org",
    "posteo.net", "hushmail.com", "runbox.com", "mailfence.com",
    "gawab.com", "europe.com", "cheerful.com", "doctor.com",
    "email.com", "engineer.com", "europe.com", "financier.com",
    "iname.com", "mail.md", "musician.org", "techie.com",
    "writeme.com"
}

# List of known disposable/temporary email providers (extendable)
DISPOSABLE_EMAIL_PROVIDERS = {
    "mailinator.com", "tempmail.com", "10minutemail.com", "guerrillamail.com",
    "trashmail.com", "dispostable.com", "getnada.com", "fakemailgenerator.com", "yop.com"
}

# def is_temporary_email(email):
#     """Check if an email is disposable using an external API"""
#     api_key = "fa5d0fd479bc461089c27b58748c1250"
#     response = requests.get(f"https://emailvalidation.abstractapi.com/v1/?api_key={api_key}&email={email}")
#     data = response.json()
    
#     return data.get("is_disposable_email", False)


class SendEmailThrottle(UserRateThrottle):
    rate = '5/hour'

    
def format_email(email):
    return email.lower()


class UserRegistrationView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        form = UserRegisterForm(data=request.data)
        
        email = request.data.get("email")
        
        check_user = User.objects.filter(email=email).first()
        
        if check_user:
            if check_user.deactivated_at:
                return Response(
                        apiResponse(
                            success=False,
                            errors=[{"message": deactivation_message}],
                        ),
                        status=CUSTOM_DEACTIVATED_ACCOUNT_STATUS
                    )
        
        if form.is_valid():
            user = form.save(commit=False)
            
            email = format_email(user.email)
            domain = format_email(email.split('@')[-1])
            
            # Reject if it's a known temporary/disposable email
            if domain in DISPOSABLE_EMAIL_PROVIDERS:
                return Response(apiResponse(
                    message="Temporary email addresses are not allowed.",
                    success=False
                ), status=status.HTTP_400_BAD_REQUEST)

            # if is_temporary_email(email):

            #     return Response(apiResponse(
            #         message="Temporary email addresses are not allowed.",
            #         success=False
            #     ), status=400)

            # Determine user type
            if domain in FREE_EMAIL_PROVIDERS:
                user.user_type = "user_viewer"
            else:
                user.user_type = "company"
                
            user.save()
            send_verification_email(user, request)
            return Response(apiResponse(message="Account has been created successfully. Please check your mail for instruction on how to verify your account."), status=status.HTTP_201_CREATED)
        return Response(apiResponse(message="Account creation failed", errors=[form.errors], success=False), status=status.HTTP_400_BAD_REQUEST)


class UserSignInView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        email = format_email(request.data.get('email'))
        password = request.data.get('password')

        # Validate that both email and password are provided
        if not email or not password:
            return Response(
                apiResponse(success=False, errors=[{"message": "Both email and password are required"}]),
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Authenticate the user
        user = authenticate(request=request, email=email, password=password)

        # Check if user exists
        if user:
            if user.deactivated_at:
                
                return Response(
                    apiResponse(
                        success=False,
                        errors=[{"message": deactivation_message}],
                    ),
                    status=CUSTOM_DEACTIVATED_ACCOUNT_STATUS
                )
            if not user.is_active:
                "Send verification email if the user is inactive"
                
                send_verification_email(user, request)
                return Response(
                    apiResponse(
                        success=False,
                        errors=[{"message": "Your account is not verified. Please check your mail to verify your account."}],
                    ),
                    status=status.HTTP_423_LOCKED,
                )
            
            domain = format_email(email.split('@')[-1])
            
            # Determine user type
            if domain in FREE_EMAIL_PROVIDERS:
                user.user_type = "user_viewer"
            else:
                user.user_type = "company"
           
            user.save()
            
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            refresh_token = str(refresh)
            user_data = {
                'id': user.id,
                'email': user.email,
                'tokens': {
                    'access': access_token,
                    'refresh': refresh_token,
                }
            }
            return Response(
                apiResponse(message="User login was successful", results=user_data),
                status=status.HTTP_200_OK,
            )
        else:
            # Authentication failed
            return Response(
                apiResponse(success=False, errors=[{"message": "Invalid login credentials"}]),
                status=status.HTTP_400_BAD_REQUEST,
            )


@api_view(['POST'])
@permission_classes([AllowAny])
@throttle_classes([SendEmailThrottle])
def request_password_reset(request):
    email = format_email(request.data.get('email'))
    
    if not email:
        return Response({"message": "Email is required"}, status=status.HTTP_400_BAD_REQUEST)

    try:
        user = User.objects.filter(email=email).first()
        
        if user:
            if not user.is_active:
                send_verification_email(user, request)
                return Response(
                    apiResponse(
                        success=False,
                        errors=[{"message": "Your account is not active. An account activation email has been sent to your email address"}],
                    ),
                    status=401
                )
            if user.deactivated_at:
                return Response(
                    apiResponse(
                        success=False,
                        errors=[{"message": deactivation_message}],
                    ),
                    status=CUSTOM_DEACTIVATED_ACCOUNT_STATUS
                )

            send_reset_password_email(user, request)
            
        return Response(apiResponse(message=f"If an account exists a password reset instructions will be sent to your email account"), status=status.HTTP_200_OK)
    except Exception as e:
        if isinstance(e, UserRateThrottle):
            return Response(apiResponse(message=throttle_message, success=False), status=status.HTTP_429_TOO_MANY_REQUESTS)
        return Response(apiResponse(message="An error occurred", success=False, errors=[{"message": str(e)}]), status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def password_reset_confirm(request, uidb64, token):
    new_password = request.data.get('new_password')
    if not new_password:
        return Response({"message": "New password is required"}, status=status.HTTP_400_BAD_REQUEST)

    try:
        user_id = force_str(urlsafe_base64_decode(uidb64))
        user = User.objects.get(pk=user_id)

        if not token_generator.check_token(user, token):
            return Response({"message": "Invalid or expired token"}, status=status.HTTP_400_BAD_REQUEST)

        user.set_password(new_password)
        user.save()
        return Response(apiResponse(message="Password reset successfully"), status=status.HTTP_200_OK)
    except (User.DoesNotExist, ValueError, TypeError, OverflowError):
        return Response({"message": "Invalid token or user does not exist"}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
@throttle_classes([SendEmailThrottle])
def resend_verification_email(request):
    email = format_email(request.data.get('email'))
    if not email:
        return Response({"message": "Email is required"}, status=status.HTTP_400_BAD_REQUEST)

    try:
        user = User.objects.get(email=email)
        if user.is_active:
            return Response({"message": f"Account with {email} is already active"}, status=status.HTTP_400_BAD_REQUEST)
        send_verification_email(user, request)
        return Response(apiResponse(message=f"Verification email has been sent to {email}"), status=status.HTTP_200_OK)
    except Exception as e:
        if isinstance(e, UserRateThrottle):
            return Response(apiResponse(message=throttle_message, success=False), status=status.HTTP_429_TOO_MANY_REQUESTS)
        return Response(apiResponse(message="An error occurred", success=False, errors=[{"message": str(e)}]), status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([AllowAny])
def verify_account(request, uidb64, token):
    try:
        user_id = force_str(urlsafe_base64_decode(uidb64))
        user = User.objects.get(pk=user_id)

        if token_generator.check_token(user, token):
            user.is_active = True
            user.save()
            return Response(apiResponse(message=f"{user.email} verified successfully"), status=status.HTTP_200_OK)
        else:
            return Response({"message": "Invalid or expired token"}, status=status.HTTP_400_BAD_REQUEST)
    except (User.DoesNotExist, ValueError, TypeError, OverflowError):
        return Response({"message": "Invalid token or user"}, status=status.HTTP_400_BAD_REQUEST)


class DeactivateAccountView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        
        try:
            email = format_email(request.data.get('email'))
            password = request.data.get('password')
            
            # Validate that both email and password are provided
            if not email or not password:
                return Response(
                    apiResponse(success=False, errors=[{"message": "Both email and password are required"}]),
                    status=status.HTTP_400_BAD_REQUEST,
                )
            
            # Authenticate the user
            user = authenticate(request=request, email=email, password=password)
            
            if not user:
                return Response(apiResponse(message="Invalid credentials"), status=status.HTTP_400_BAD_REQUEST)
            
            if user.deactivated_at:
                return Response(
                    apiResponse(
                        success=False, errors=[{"message": "Account is already deactivated"}],
                    ),
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            with transaction.atomic():
                user.deactivated_at = now()
                user.save()

                transaction.on_commit(lambda: send_deactivation_email_to_admin(user))

            return Response(apiResponse(message="Account deactivation was successful"), status=200)
        except Exception as e:
            return Response(apiResponse(message="Account deactivation failed", success=False, errors=[{"message": "Account deactivation was not successful"}, {"server": f"{e}"}]), status=status.HTTP_400_BAD_REQUEST)


class RequestAccountReactivation(APIView):

    permission_classes = [AllowAny]
    throttle_classes = [SendEmailThrottle]

    def post(self, request):

        email = format_email(request.data.get('email'))
        
        if not email:
            return Response({"message": "Email is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.filter(email=email).first()
            
            if user:
                if not user.deactivated_at:
                    return Response(
                        apiResponse(
                            success=False,
                            errors=[{"message": "Your account is not deactivated. Please proceed to sign in"}],
                        ),
                        status=CUSTOM_REACTIVATED_ACCOUNT_STATUS
                    )

                send_reactivation_email(user)
                
            return Response(apiResponse(message=f"If an account exists an account reactivation instructions will be sent to the email account"), status=status.HTTP_200_OK)
        except Exception as e:
            if isinstance(e, UserRateThrottle):
                return Response(apiResponse(message=throttle_message, success=False), status=status.HTTP_429_TOO_MANY_REQUESTS)
            return Response(apiResponse(message="An error occurred", success=False, errors=[{"message": str(e)}]), status=status.HTTP_400_BAD_REQUEST)


class ReactivateAccountView(APIView):
    permission_classes = [AllowAny]

    def post(self, request, uidb64, token):
        try:
            user_id = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(pk=user_id)

            if token_generator.check_token(user, token):
                user.deactivated_at = None
                user.save()
                send_reactivation_notification_email(user)
                return Response(apiResponse(message=f"{user.email} account reactivated successfully"), status=status.HTTP_200_OK)
            else:
                return Response({"message": "Invalid or expired token"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(apiResponse(message="Reactivation failed", success=False, errors=[{"message": "Reactivation was not successful"}, {"server": f"{e}"}]), status=status.HTTP_400_BAD_REQUEST)


class ChangePasswordView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            user = request.user
            old_password = request.data.get('old_password')
            new_password = request.data.get('new_password')

            if not old_password or not new_password:
                return Response(
                    apiResponse(success=False, errors=[{"message": "Both old password and new password are required"}]),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not user.check_password(old_password):
                return Response(
                    apiResponse(success=False, errors=[{"message": "Old password is incorrect"}]),
                    status=status.HTTP_400_BAD_REQUEST,
                )

            user.set_password(new_password)
            user.save()
            send_password_change_email(user)
            return Response(apiResponse(message="Password changed successfully"), status=status.HTTP_200_OK)
        except Exception as e:
            return Response(apiResponse(message="Password change failed", success=False, errors=[{"message": str(e)}]), status=status.HTTP_400_BAD_REQUEST)


class LogoutView(APIView):
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        try:
            refresh_token = request.data['refresh']
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response(apiResponse(message="Logout was successful"), status=status.HTTP_204_NO_CONTENT)
        except Exception as e:
            return Response(apiResponse(message="Logout failed", success=False, errors=[{"message": "Logout was not successful"}, {"server": f"{e}"}]), status=status.HTTP_400_BAD_REQUEST)
