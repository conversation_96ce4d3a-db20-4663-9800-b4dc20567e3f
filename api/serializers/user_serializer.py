from rest_framework import serializers
from authentication.models import User, Gender
from api.models.comment_model import FollowingRelationships

class BasicUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            "id",
            "full_name",
            "email",
            "avatar",
            "verified",
            "country",
            "city",
            "phone_number",
            "region",
            "address",
        ]

class UserSerializer(serializers.HyperlinkedModelSerializer):
    gender = serializers.SlugRelatedField(queryset=Gender.objects.all(), slug_field='type')
    companies = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    followers_count = serializers.IntegerField(read_only=True)
    following_count = serializers.IntegerField(read_only=True)
 
    followers = serializers.SerializerMethodField()
    followings = serializers.SerializerMethodField()
    active_company = serializers.SerializerMethodField()
    company_followings = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'first_name', 'last_name', 'email', 'gender', 'date_of_birth', 'bio', 
            'role', 'company', 'verified', 'country', 'city', 
            'phone_number', 'region', 'address', 'avatar', 
            'is_first_time_user','user_type', 'followers', 
            'followings', "followers_count", "following_count", 
            'companies', 'active_company', 'company_followings'
        ]
        
    def get_followers(self, obj):
        """Returns a list of user IDs of users who follow the given user."""
        return list(obj.user_followers.values_list('user_follower_id', flat=True))

    def get_followings(self, obj):
        """
        Returns a list of all followings (users and companies) for the given user.
        Each entry is a dict with 'type' ('user' or 'company') and 'id'.
        """
        followings = []
        # User followings
        for user_id in obj.user_following.values_list('user_following_id', flat=True):
            if user_id is not None:
                followings.append({'type': 'user', 'id': user_id})
        # Company followings
        for company_id in FollowingRelationships.objects.filter(
            user_follower=obj, company_following__isnull=False
        ).values_list('company_following_id', flat=True):
            if company_id is not None:
                followings.append({'type': 'company', 'id': company_id})
        return followings

    def get_active_company(self, obj):
        """Returns a user active company information."""
        return None

    def get_company_followings(self, obj):
        """Returns a list of company IDs the given user is following (excludes nulls)."""
        return list(
            FollowingRelationships.objects.filter(
                user_follower=obj, company_following__isnull=False
            ).values_list('company_following_id', flat=True)
        )


class GenderSerializer(serializers.ModelSerializer):

    class Meta:
        model = Gender
        fields = ['pk', 'type']
