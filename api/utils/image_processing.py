"""
Image processing utilities for generating Low Quality Image Placeholders (LQIP).
"""

import base64
import io
import logging
from typing import Op<PERSON>, Tuple

from PIL import Image, ImageOps

logger = logging.getLogger(__name__)


class ImageProcessor:
    """
    Utility class for processing images and generating LQIP thumbnails.
    """

    # LQIP configuration
    LQIP_MAX_SIZE = (100, 100)
    LQIP_QUALITY = 30
    LQIP_FORMAT = 'JPEG'

    @classmethod
    def generate_lqip_base64(cls, image_file) -> Optional[str]:
        """
        Generate a Low Quality Image Placeholder (LQIP) as a base64-encoded string.

        Args:
            image_file: Django uploaded file (InMemoryUploadedFile or TemporaryUploadedFile)

        Returns:
            str: Base64-encoded LQIP thumbnail, or None if processing fails
        """
        try:
            # Reset file pointer to beginning
            image_file.seek(0)

            # Open the image with Pillow
            with Image.open(image_file) as img:
                if img.mode in ('RGBA', 'LA', 'P'):
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')

                # Generate thumbnail while maintaining aspect ratio
                thumbnail = cls._create_thumbnail(img, cls.LQIP_MAX_SIZE)

                return cls._image_to_base64(thumbnail, cls.LQIP_FORMAT, cls.LQIP_QUALITY)

        except Exception as e:
            logger.error(f"Failed to generate LQIP for image: {str(e)}")
            return None
        finally:
            # Reset file pointer for subsequent use
            image_file.seek(0)

    @classmethod
    def _create_thumbnail(cls, image: Image.Image, max_size: Tuple[int, int]) -> Image.Image:
        """
        Create a thumbnail of the image while maintaining aspect ratio.

        Args:
            image: PIL Image object
            max_size: Maximum dimensions (width, height)

        Returns:
            PIL Image object (thumbnail)
        """
        thumbnail = ImageOps.fit(
            image,
            max_size,
            Image.Resampling.LANCZOS,
            centering=(0.5, 0.5)
        )
        return thumbnail

    @classmethod
    def _image_to_base64(cls, image: Image.Image, format_name: str, quality: int) -> str:
        """
        Convert PIL Image to base64-encoded string.

        Args:
            image: PIL Image object
            format_name: Output format ('JPEG', 'PNG', etc.)
            quality: JPEG quality (1-100)

        Returns:
            str: Base64-encoded image with data URI prefix
        """
        buffer = io.BytesIO()

        # Save image to buffer
        save_kwargs = {'format': format_name}
        if format_name.upper() == 'JPEG':
            save_kwargs['quality'] = quality
            save_kwargs['optimize'] = True

        image.save(buffer, **save_kwargs)

        # Get the image data
        image_data = buffer.getvalue()

        # Encode to base64
        base64_encoded = base64.b64encode(image_data).decode('utf-8')

        mime_type = f"image/{format_name.lower()}"
        data_uri = f"data:{mime_type};base64,{base64_encoded}"

        return data_uri

    @classmethod
    def get_image_info(cls, image_file) -> Optional[dict]:
        """
        Get basic information about an image file.

        Args:
            image_file: Django uploaded file

        Returns:
            dict: Image information (width, height, format, size) or None if processing fails
        """
        try:
            image_file.seek(0)

            with Image.open(image_file) as img:
                info = {
                    'width': img.width,
                    'height': img.height,
                    'format': img.format,
                    'mode': img.mode,
                    'size_bytes': image_file.size
                }
                return info

        except Exception as e:
            logger.error(f"Failed to get image info: {str(e)}")
            return None
        finally:
            image_file.seek(0)

    @classmethod
    def validate_image(cls, image_file) -> Tuple[bool, Optional[str]]:
        """
        Validate if the uploaded file is a valid image.

        Args:
            image_file: Django uploaded file

        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        try:
            image_file.seek(0)

            with Image.open(image_file) as img:
                img.verify()

            return True, None

        except Exception as e:
            error_msg = f"Invalid image file: {str(e)}"
            logger.warning(error_msg)
            return False, error_msg
        finally:
            image_file.seek(0)


def generate_message_image_preview(image_file) -> Optional[dict]:
    """
    Convenience function to generate LQIP and extract dimensions for message images.

    Args:
        image_file: Django uploaded file

    Returns:
        dict: Dictionary containing 'preview' (base64 LQIP) and 'dimensions' (width, height)
              or None if processing fails
    """
    # Validate the image first
    is_valid, error_msg = ImageProcessor.validate_image(image_file)
    if not is_valid:
        logger.warning(f"Invalid image file for preview generation: {error_msg}")
        return None

    # Check file size (optional - prevent processing very large files)
    max_size_mb = 30
    if hasattr(image_file, 'size') and image_file.size > max_size_mb * 1024 * 1024:
        logger.warning(f"Image file too large for preview generation: {image_file.size} bytes")
        return None

    # Get image information including dimensions
    image_info = ImageProcessor.get_image_info(image_file)
    if not image_info:
        logger.warning("Failed to extract image information")
        return None

    # Generate LQIP preview
    preview_data = ImageProcessor.generate_lqip_base64(image_file)
    if not preview_data:
        logger.warning("Failed to generate LQIP preview")
        return None

    return {
        'preview': preview_data,
        'dimensions': {
            'width': image_info['width'],
            'height': image_info['height']
        }
    }
