"""
Utility functions for handling file cleanup in both local and S3 storage environments.
"""

import logging
from typing import List, Tuple

from django.core.files.storage import default_storage

logger = logging.getLogger(__name__)


def delete_image_files(image_instances) -> Tuple[int, int]:
    """
    Delete image files for a list of ProductImage instances.

    Args:
        image_instances: QuerySet or list of ProductImage instances

    Returns:
        Tuple of (successful_deletions, failed_deletions)
    """
    successful_deletions = 0
    failed_deletions = 0

    for image in image_instances:
        if image.image:
            try:
                if default_storage.exists(image.image.name):
                    default_storage.delete(image.image.name)
                    logger.info(f"Successfully deleted image file: {image.image.name}")
                    successful_deletions += 1
                else:
                    logger.warning(f"Image file not found in storage: {image.image.name}")
                    # Count as successful since the file doesn't exist anyway
                    successful_deletions += 1
            except Exception as e:
                logger.error(f"Failed to delete image file {image.image.name}: {str(e)}")
                failed_deletions += 1

    return successful_deletions, failed_deletions


def cleanup_orphaned_files(storage_path: str = 'product/') -> Tuple[int, int]:
    """
    Clean up orphaned files in storage that don't have corresponding database records.

    This is a more advanced cleanup that can be run periodically to remove files
    that may have been left behind due to failed operations.

    Args:
        storage_path: The storage path to clean up (e.g., 'product/')

    Returns:
        Tuple of (files_found, files_deleted)
    """
    from api.models.product_model import ProductImage

    try:
        # Get all files in the storage path
        if not default_storage.exists(storage_path):
            logger.info(f"Storage path {storage_path} does not exist")
            return 0, 0

        # List all files in the directory
        directories, files = default_storage.listdir(storage_path)

        # Get all image file names from the database
        db_image_names = set(
            ProductImage.objects.values_list('image', flat=True)
            .exclude(image='')
            .exclude(image__isnull=True)
        )

        files_found = len(files)
        files_deleted = 0

        for file_name in files:
            full_path = f"{storage_path}{file_name}"

            # Check if this file exists in the database
            if full_path not in db_image_names:
                try:
                    default_storage.delete(full_path)
                    logger.info(f"Deleted orphaned file: {full_path}")
                    files_deleted += 1
                except Exception as e:
                    logger.error(f"Failed to delete orphaned file {full_path}: {str(e)}")

        return files_found, files_deleted

    except Exception as e:
        logger.error(f"Error during orphaned file cleanup: {str(e)}")
        return 0, 0


def get_storage_info() -> dict:
    """
    Get information about the current storage backend.

    Returns:
        Dictionary with storage backend information
    """
    storage_info = {
        'backend': type(default_storage).__name__,
        'location': getattr(default_storage, 'location', 'N/A'),
    }

    # Add S3-specific info if using S3
    if hasattr(default_storage, 'bucket_name'):
        storage_info.update({
            'bucket_name': default_storage.bucket_name,
            'region': getattr(default_storage, 'region_name', 'N/A'),
        })

    return storage_info
