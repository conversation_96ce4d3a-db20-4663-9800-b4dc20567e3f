from django.urls import include, path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from api.views import (
    advanced_search_view,
    comment_view,
    company_view,
    document_view,
    follow_view,
    post_view,
    product_view,
    representative_view,
    search_view,
    service_view,
    user_view,
)
from api.views.comment_view import ReplyViewSet, ReviewViewSet
from chat.views import GroupMessageViewSet, MessageViewSet
from notification.views import NotificationView

# from search_indexes.viewsets.search_viewset import CompanyDocumentView, ProductDocumentView, PostDocumentView, ServiceDocumentView

app_name = "api"

# Create a router and register our ViewSets with it.
router = DefaultRouter()
router.register(r'companies', company_view.CompanyViewSet, basename='company')
router.register(r'users', user_view.UserViewSet, basename='user')
router.register(r'genders', user_view.GenderViewSet, basename='gender')
router.register(r'company-categories', company_view.CompanyCategoryViewSet, basename='company-category')
router.register(r'company-sizes', company_view.CompanySizeViewSet, basename='company-size')
router.register(r'documents', document_view.DocumentViewSet, basename='document')
router.register(r'document-types', document_view.DocumentTypeViewSet, basename='document-type')
router.register(r'posts', post_view.PostViewSet, basename='post')
router.register(r'products', product_view.ProductViewSet, basename='product')
router.register(r'product-images', product_view.ProductImageViewSet, basename='product-image')
router.register(r'product-categories', product_view.ProductCategoryViewSet, basename='product-category')
router.register(r'services', service_view.ServiceViewSet, basename='service')
router.register(r'service-images', service_view.ServiceImageViewSet, basename='service-image')
router.register(r'service-categories', service_view.ServiceCategoryViewSet, basename='service-category')
router.register(r'likes', follow_view.LikeViewSet, basename='like')
router.register(r'comments', comment_view.CommentViewSet, basename='comment')
router.register(r'follows', follow_view.FollowViewSet, basename='follow')
router.register(r'reviews', ReviewViewSet, basename='review')
router.register(r'replies', ReplyViewSet, basename='reply')
router.register(r'representatives', representative_view.RepresentativeViewSet, basename='representative')
router.register(r'representative-categories', representative_view.RepresentativeCategoryViewSet, basename='representative-category')
router.register(r'messages', MessageViewSet, basename='messages')
router.register(r'notifications', NotificationView, basename='notifications')
router.register(r'group-messages', GroupMessageViewSet, basename='group-message')



# router.register(r"company-search", CompanyDocumentView, basename="search_company")
# router.register(r"product-search", ProductDocumentView, basename="search_product")
# router.register(r"post-search", PostDocumentView, basename="search_post")
# router.register(r"service-search", ServiceDocumentView, basename="search_service")

urlpatterns = [
    path('', include(router.urls)),
    path('current-user/', user_view.CurrentUserProfileView.as_view(), name='current-user'),
    path('search/', search_view.SearchView.as_view(), name='search'),
    path('search/advanced/', advanced_search_view.AdvancedSearchView.as_view(), name='advanced-search'),
    path('search/autocomplete/', advanced_search_view.AutocompleteView.as_view(), name='autocomplete'),
]
