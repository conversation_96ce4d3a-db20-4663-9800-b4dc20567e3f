from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response

from api.models.comment_model import Comment, FollowingRelationships
from api.models.company_model import Company, CompanyCategory, CompanySize
from api.serializers.all_serializer import (
    CommentSerializer,
    CompanyCategorySerializer,
    CompanySerializer,
    CompanySizeSerializer,
)


class CompanyPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100


class CompanyViewSet(viewsets.ModelViewSet):
    queryset = Company.objects.all()
    serializer_class = CompanySerializer
    lookup_field = 'slug'
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['organization_type', 'company_size', 'country', 'date_created']
    search_fields = [ '^company_name' ]
    ordering_fields = [ 'company_name', 'date_created']
    ordering = ['date_created']
    pagination_class = CompanyPagination
    
    def get_queryset(self):
        queryset = Company.objects.select_related(
            "profile", "company_size", "organization_type"
        ).prefetch_related(
            "documents",
            "products__images",
            "products__category",
            "services__images",
            "services__category",
            "reviews__user",
            "company_followers",
            "company_following",

        )
        profile_email = self.request.query_params.get("profile")
        company_id = self.request.query_params.get("id")
        if profile_email:
            queryset = queryset.filter(profile__email=profile_email)
        if company_id:
            queryset = queryset.filter(id=company_id)
        return queryset

    def perform_create(self, serializer):
        serializer.save(profile=self.request.user)
    
    @action(detail=True, methods=['post'], url_path='follow')
    def follow_company(self, request, slug=None):
        company = self.get_object()
        user = request.user
        # follower_company = request.data.get('company_id')
        follow, created = FollowingRelationships.objects.get_or_create(
            company_following=company,
            user_follower=user if user.is_authenticated else None,
            company_follower_id=company.id,
        )
        if not created:
            return Response({"detail": "You are already following this company."}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"message": f"{company.company_name} followed."}, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'], url_path='unfollow')
    def unfollow_company(self, request, slug=None):
        company = self.get_object()
        user = request.user
        # follower_company = request.data.get('company_id')
        follow = FollowingRelationships.objects.filter(company_following=company, user_follower=user if user.is_authenticated else None, company_follower_id=company.id).first()
        if not follow:
            return Response({"detail": "You are not following this company."}, status=status.HTTP_400_BAD_REQUEST)
        follow.delete()
        return Response({"message": f"{company.company_name} unfollowed."}, status=status.HTTP_204_NO_CONTENT)
    
    @action(detail=True, methods=['post'], url_path='review')
    def review_company(self, request, pk=None):

        company = self.get_object()
        user = request.user
        company_id = request.data.get('company_id')
        user_review = request.data.get('review')

        if not user_review:
            return Response({"detail": "Please enter a review"}, status=status.HTTP_400_BAD_REQUEST)

        review = Comment.objects.create(
            company=company,
            user=user if user.is_authenticated else None,
            company_id=company_id if company_id else None,
            content=user_review
        )
        
        return Response(CommentSerializer(review).data, status=status.HTTP_201_CREATED)


class CompanyCategoryViewSet(viewsets.ModelViewSet):
    queryset = CompanyCategory.objects.all()
    serializer_class = CompanyCategorySerializer


class CompanySizeViewSet(viewsets.ModelViewSet):
    queryset = CompanySize.objects.all()
    serializer_class = CompanySizeSerializer
