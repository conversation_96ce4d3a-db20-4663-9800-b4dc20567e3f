from django.db.models import Q
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from api.models import Company, Post, Product, Service
from authentication.models import User
from api.serializers.all_serializer import (
    CompanySerializer,
    PostSerializer,
    ProductSerializer,
    ServiceSerializer,
)

class SearchView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, format=None):
        query = request.query_params.get('q', '')

        if not query:
            return Response({"error": "A query parameter 'q' is required."}, status=400)

        # Search Companies
        companies = Company.objects.filter(
            Q(company_name__icontains=query) | Q(about__icontains=query) | Q(country__icontains=query) | Q(city__icontains=query) | Q(tag_line__icontains=query)
        )
        company_serializer = CompanySerializer(companies, many=True)

        # Search Posts
        posts = Post.objects.filter(
            Q(body__icontains=query) | Q(company__company_name__icontains=query)
        )
        post_serializer = PostSerializer(posts, many=True)

        # Search Products
        products = Product.objects.filter(
            Q(title__icontains=query) | Q(sub_title__icontains=query) | Q(description__icontains=query) | Q(category__name__icontains=query) | Q(company__company_name__icontains=query)
        )
        product_serializer = ProductSerializer(products, many=True)

        # Search Services
        services = Service.objects.filter(
            Q(title__icontains=query) | Q(sub_title__icontains=query) | Q(description__icontains=query) | Q(category__name__icontains=query) | Q(company__company_name__icontains=query)
        )
        service_serializer = ServiceSerializer(services, many=True)

        # Search Users
        users = User.objects.filter(
            Q(first_name__icontains=query) | Q(last_name__icontains=query) | Q(email__icontains=query)
        )
        user_data = users.values('id', 'first_name', 'last_name', 'email')

        # Combine results
        return Response({
            "companies": company_serializer.data,
            "posts": post_serializer.data,
            "products": product_serializer.data,
            "services": service_serializer.data,
            "users": list(user_data),
        })
