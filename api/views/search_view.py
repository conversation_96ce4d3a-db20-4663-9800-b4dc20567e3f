from django.db.models import Q
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView

from api.models import Company, Post, Product, Service
from api.serializers.all_serializer import (
    CompanySerializer,
    PostSerializer,
    ProductSerializer,
    ServiceSerializer,
)
from authentication.models import User


class SearchPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 50


class SearchView(APIView):
    permission_classes = [AllowAny]

    def get(self, request, format=None):
        query = request.query_params.get('q', '')
        limit = min(int(request.query_params.get('limit', 20)), 50)

        if not query:
            return Response(
                {"error": "A query parameter 'q' is required."},
                status=400
            )

        # Optimized search with select_related and limits
        # Search Companies
        companies = Company.objects.select_related(
            'organization_type', 'company_size'
        ).filter(
            Q(company_name__icontains=query) |
            Q(about__icontains=query) |
            Q(country__icontains=query) |
            Q(city__icontains=query) |
            Q(tag_line__icontains=query)
        )[:limit]

        # Search Posts with company info
        posts = Post.objects.select_related('company', 'user').filter(
            Q(body__icontains=query) |
            Q(company__company_name__icontains=query)
        )[:limit]

        # Search Products with related data
        products = Product.objects.select_related(
            'category', 'company'
        ).filter(
            Q(title__icontains=query) |
            Q(sub_title__icontains=query) |
            Q(description__icontains=query) |
            Q(category__name__icontains=query) |
            Q(company__company_name__icontains=query)
        )[:limit]

        # Search Services with related data
        services = Service.objects.select_related(
            'category', 'company'
        ).filter(
            Q(title__icontains=query) |
            Q(sub_title__icontains=query) |
            Q(description__icontains=query) |
            Q(category__name__icontains=query) |
            Q(company__company_name__icontains=query)
        )[:limit]

        # Search Users (limited fields for performance)
        users = User.objects.filter(
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(email__icontains=query)
        ).values(
            'id', 'first_name', 'last_name', 'email'
        )[:limit]

        # Serialize results
        company_serializer = CompanySerializer(companies, many=True)
        post_serializer = PostSerializer(posts, many=True)
        product_serializer = ProductSerializer(products, many=True)
        service_serializer = ServiceSerializer(services, many=True)

        # Combine results with metadata
        return Response({
            "query": query,
            "limit": limit,
            "results": {
                "companies": {
                    "count": len(companies),
                    "data": company_serializer.data
                },
                "posts": {
                    "count": len(posts),
                    "data": post_serializer.data
                },
                "products": {
                    "count": len(products),
                    "data": product_serializer.data
                },
                "services": {
                    "count": len(services),
                    "data": service_serializer.data
                },
                "users": {
                    "count": len(users),
                    "data": list(users)
                }
            }
        })
