from django_filters.rest_framework import Django<PERSON>ilterB<PERSON>end
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import PermissionDenied
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from api.models.comment_model import Like
from api.models.company_model import Company
from api.models.service_model import Service, ServiceCategory, ServiceImage
from api.permissions import IsCompanyOwnerOrReadOnly
from api.serializers.all_serializer import (
    ServiceCategorySerializer,
    ServiceImageSerializer,
    ServiceSerializer,
)
from authentication.models import User
from notification.notification_service import NotificationService
from responselib.helpers import apiResponse


class ServicePagination(PageNumberPagination):
    page_size = 12
    page_size_query_param = 'page_size'
    max_page_size = 20


class ServiceViewSet(viewsets.ModelViewSet):
    queryset = Service.objects.all().order_by('-date_created')
    serializer_class = ServiceSerializer
    pagination_class = ServicePagination
    # permission_classes = [IsAuthenticated, IsCompanyOwnerOrReadOnly]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['category', 'company', 'featured', 'date_created']
    search_fields = (
        '^title',
        '^sub_title',
    )
    ordering_fields = (
        'title',
        'date_created'
    )

    def perform_create(self, serializer):
        company_name = self.request.data.get('company')
        company = Company.objects.get(company_name=company_name)

        # Ensure user owns the company
        if company.profile != self.request.user:
            raise PermissionDenied("You can only create services for your own company")

        serializer.save(company=company)

    def perform_update(self, serializer):
        # Ensure user owns the company associated with the service
        service = self.get_object()
        if service.company.profile != self.request.user:
            raise PermissionDenied("You can only edit your company's services")
        serializer.save()

    def perform_destroy(self, instance):
        # Ensure user owns the company associated with the service
        if instance.company.profile != self.request.user:
            raise PermissionDenied("You can only delete your company's services")
        instance.delete()

    @action(detail=True, methods=['post'], url_path='like')
    def like_service(self, request, pk=None):
        service = self.get_object()
        user = request.user
        company = Company.objects.get(company_name=self.request.data.get('company'))
        recipient = User.objects.get(companies__id=company.id)
        
        if not user.is_authenticated:
            return Response({"detail": "Authentication is required."}, status=status.HTTP_401_UNAUTHORIZED)
        
        like, created = Like.objects.get_or_create(
            service=service,
            user=user,
            company_id=company.id
        )
        if not created:
            return Response({"detail": "You have already bookmarked this service."}, status=status.HTTP_400_BAD_REQUEST)
        
        # Create notification for the service owner
        NotificationService.create_notification(
            recipient=user,
            sender=user,
            notification_type='bookmark',
            title=service.title,
            message=f"You bookmarked {service.title.lower()}.",
            link=f"/services/{service.id}/"
        )
        
        if user.id != recipient.id:
            # Create notification for the product owner
            NotificationService.create_notification(
                recipient=recipient,
                sender=user,
                notification_type='bookmark',
                title=service.title,
                message=f"{user.first_name.title()} bookmarked your service {service.title.lower()}.",
                link=f"/services/{service.id}/"
            )
        return Response(apiResponse(message=f"{service.title.title()} added to bookmark."), status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'], url_path='unlike')
    def unlike_service(self, request, pk=None):
        service = self.get_object()
        user = request.user
        if not user.is_authenticated:
            return Response({"detail": "Authentication is required."}, status=status.HTTP_401_UNAUTHORIZED)
        
        like = Like.objects.filter(service=service, user=user if user.is_authenticated else None).first()
        if not like:
            return Response({"detail": "You have not bookmarked this service."}, status=status.HTTP_400_BAD_REQUEST)
        like.delete()
        return Response(apiResponse(message=f"{service.title.title()} has been removed from bookmarked."), status=status.HTTP_204_NO_CONTENT)


class ServiceCategoryViewSet(viewsets.ModelViewSet):
    queryset = ServiceCategory.objects.all()
    serializer_class = ServiceCategorySerializer

class ServiceImageViewSet(viewsets.ModelViewSet):
    queryset = ServiceImage.objects.all()
    serializer_class = ServiceImageSerializer
    pagination_class = ServicePagination
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['service',]
