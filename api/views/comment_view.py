from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response

from api.models.comment_model import Comment, Like, Reply, Review
from api.models.company_model import Company
from api.serializers.all_serializer import (
    CommentSerializer,
    ReplySerializer,
    ReviewSerializer,
)
from notification.notification_service import NotificationService


class CommentPagination(PageNumberPagination):
    page_size = 25
    page_size_query_param = 'page_size'
    max_page_size = 50


class CommentViewSet(viewsets.ModelViewSet):
    queryset = Comment.objects.all()
    serializer_class = CommentSerializer
    pagination_class = CommentPagination

    def perform_create(self, serializer):
        user = self.request.user if 'user' in self.request.data else None
        company = Company.objects.get(pk=self.request.data['company']) if 'company' in self.request.data else None
        serializer.save(user=user, company=company)

    @action(detail=True, methods=['post'], url_path='like')
    def like_comment(self, request, pk=None):
        post = self.get_object()
        user = request.user
        company = request.data.get('company_id')
        like, created = Like.objects.get_or_create(post=post, user=user if user.is_authenticated else None, company_id=company if company else None)
        if not created:
            return Response({"detail": "Already liked this comment."}, status=status.HTTP_400_BAD_REQUEST)
        
        NotificationService.create_notification(
            self,
            recipient=post.user,
            sender=user,
            notification_type='like',
            title="Post Liked",
            message=f"{user.username} liked your comment.",
            link=f"/posts/{post.id}/"
        )
        return Response({"detail": "Post liked."}, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'], url_path='unlike')
    def unlike_comment(self, request, pk=None):
        post = self.get_object()
        user = request.user
        company = request.data.get('company_id')
        like = Like.objects.filter(post=post, user=user if user.is_authenticated else None, company_id=company if company else None).first()
        if not like:
            return Response({"detail": "You have not liked this comment."}, status=status.HTTP_400_BAD_REQUEST)
        like.delete()
        return Response({"detail": "Post unlike"}, status=status.HTTP_204_NO_CONTENT)
    
    @action(detail=True, methods=['post'], url_path='reply')
    def reply_comment(self, request, pk=None):

        comment = self.get_object()
        user = request.user
        company_id = request.data.get('company_id')
        user_reply = request.data.get('reply')

        if not user_reply:
            return Response({"detail": "Please enter a reply"}, status=status.HTTP_400_BAD_REQUEST)

        reply = Reply.objects.create(
            comment=comment,
            user=user if user.is_authenticated else None,
            company_id=company_id if company_id else None,
            content=user_reply
        )
        
        NotificationService.create_notification(
            self,
            recipient=comment.user,
            sender=user,
            notification_type='reply',
            title="comment replied",
            message=f"{user.username} replied to your comment.",
            link=f"/posts/{comment.post.id}/?commentId={comment.id}"
        )
        
        return Response(CommentSerializer(reply).data, status=status.HTTP_201_CREATED)


class ReviewViewSet(viewsets.ModelViewSet):
    queryset = Review.objects.all()
    serializer_class = ReviewSerializer

    def perform_create(self, serializer):
        user = self.request.user if 'user' in self.request.data else None
        company = Company.objects.get(pk=self.request.data['company']) if 'company' in self.request.data else None
        serializer.save(user=user, company=company)


class ReplyViewSet(viewsets.ModelViewSet):
    queryset = Reply.objects.all()
    serializer_class = ReplySerializer

    def perform_create(self, serializer):
        user = self.request.user if 'user' in self.request.data else None
        company = Company.objects.get(pk=self.request.data['company']) if 'company' in self.request.data else None
        serializer.save(user=user, company=company)
