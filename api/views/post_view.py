from django_filters.rest_framework import Django<PERSON>ilterBackend
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import AllowAny
from rest_framework.response import Response

from api.models.comment_model import Comment, Like
from api.models.company_model import Company
from api.models.post_model import Post, PostImage
from api.serializers.all_serializer import CommentSerializer, PostSerializer
from notification.notification_service import NotificationService


class PostPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 50


class PostViewSet(viewsets.ModelViewSet):
    queryset = Post.objects.all()
    serializer_class = PostSerializer
    pagination_class = PostPagination
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['company', 'date_created']
    search_fields = [ '^body']
    ordering_fields = [ 'date_created']
    
    def perform_create(self, serializer):
        company = Company.objects.get(pk=self.request.data['company']) if 'company' in self.request.data else None
        images = self.request.FILES.getlist('images')

        post = serializer.save(user=self.request.user, company=company)
    
        for image in images:
            PostImage.objects.create(post=post, image=image)

    @action(detail=True, methods=['post'], url_path='like')
    def like_post(self, request, pk=None):
        post = self.get_object()
        user = request.user
        
        like, created = Like.objects.get_or_create(post=post, user=user if user.is_authenticated else None)
        if not created:
            return Response({"detail": "Already liked this post."}, status=status.HTTP_400_BAD_REQUEST)
        if post.user.id is user.id:
            NotificationService.create_notification(
            recipient=user,
            sender=user,
            notification_type='like',
            title="Liked Post",
            message="You liked your own post",
            link=f"/posts/{post.id}/"
        )
        if post.user.id is not user.id:
            # Create notification for the post owner
            NotificationService.create_notification(
                recipient=post.user,
                sender=user,
                notification_type='like',
                title="Post Liked",
                message=f"{user.first_name} liked your post.",
                link=f"/posts/{post.id}/"
            )
        
        return Response({"detail": "Post liked."}, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'], url_path='unlike')
    def unlike_post(self, request, pk=None):
        post = self.get_object()
        user = request.user
        like = Like.objects.filter(post=post, user=user if user.is_authenticated else None).first()
        if not like:
            return Response({"detail": "You have not liked this post."}, status=status.HTTP_400_BAD_REQUEST)
        like.delete()
        return Response({"detail": "Post unlike"}, status=status.HTTP_204_NO_CONTENT)
    
    @action(detail=True, methods=['post'], url_path='comment')
    def comment_post(self, request, pk=None):

        post = self.get_object()
        user = request.user
        user_comment = request.data.get('comment')

        if not user_comment:
            return Response({"detail": "Please enter a comment"}, status=status.HTTP_400_BAD_REQUEST)

        comment = Comment.objects.create(
            post=post,
            user=user if user.is_authenticated else None,
            content=user_comment
        )
        
        if post.user.id == user.id:
            NotificationService.create_notification(
            recipient=user,
            sender=user,
            notification_type='comment',
            title="Post commented",
            message="You added a comment to your post.",
            link=f"/posts/{post.id}/",
            extra_data={"comment":user_comment}
        )
        if post.user.id != user.id:
            NotificationService.create_notification(
                recipient=post.user,
                sender=user,
                notification_type='comment',
                title="Post commented",
                message=f"{user.first_name} commented on your post.",
                link=f"/posts/{post.id}/"
            )
        return Response(CommentSerializer(comment).data, status=status.HTTP_201_CREATED)
