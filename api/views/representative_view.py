from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from api.models.representative import Representative, RepresentativeCategory
from api.serializers.all_serializer import RepresentativeSerializer, RepresentativeCategorySerializer
from mailing.views import send_representation_request_email
from api.models.company_model import Company
from authentication.models import User
from rest_framework.decorators import action
from django.utils import timezone
from rest_framework.exceptions import ValidationError
from notification.notification_service import NotificationService
from django.utils.crypto import get_random_string
from django.contrib.auth.hashers import make_password


class RepresentativeCategoryViewSet(viewsets.ModelViewSet):
    queryset = RepresentativeCategory.objects.all()
    serializer_class = RepresentativeCategorySerializer
    permission_classes = [IsAuthenticated]


class RepresentativeViewSet(viewsets.ModelViewSet):
    queryset = Representative.objects.all()
    serializer_class = RepresentativeSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        queryset = super().get_queryset()
        company_id = self.request.query_params.get("company_id")
        status_param = self.request.query_params.get("status")
        if company_id:
            queryset = queryset.filter(company__id=company_id)
        if status_param:
            queryset = queryset.filter(status=status_param)
        return queryset

    def perform_create(self, serializer):
        company_id = self.request.data.get('company')
        user_id = self.request.data.get('user')

        try:
            company = Company.objects.get(id=company_id)
            user = User.objects.get(id=user_id)
        except (Company.DoesNotExist, User.DoesNotExist):
            raise ValidationError({"detail": "Invalid company or user ID"})

        if Representative.objects.filter(company=company, user=user).exists():
            raise ValidationError({"detail": "User is already a representative of this company"})

        # Generate plain token
        plain_token = get_random_string(64)
        representative = serializer.save(company=company, user=user)
        representative.token = make_password(plain_token)
        representative.save()
        NotificationService().create_notification(
            recipient=representative.user,
            sender=self.request.user,
            notification_type="representation",
            message=f"You have been invited to represent {company.company_name}", link=f"/co/representatives/accept?token={plain_token}&company={company.company_name}&rep_id={representative.id}&company_url=/{company.company_name}",
            title="Representation Request"
        )
        NotificationService().create_notification(
            recipient=self.request.user,
            sender=representative.user,
            notification_type="representation",
            message=f"You have requested a representation from {representative.user.first_name}",
            link=f"/representatives/manage",
            title="Representation Request"
        )
        send_representation_request_email(
            user=user,
            repId=representative.id,
            representation_requester=company.company_name,
            token=plain_token
        )

    @action(detail=True, methods=['post'], url_path='accept-invitation')
    def accept_invitation(self, request, pk=None):
        token = request.data.get("token")
        if not pk:
            return Response({"detail": "Representative id is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        if not token:
            return Response({"detail": "Token is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            representative = Representative.objects.get(id=pk)
        except Representative.DoesNotExist:
            return Response({"detail": "Representative does not exist"}, status=status.HTTP_400_BAD_REQUEST)

        # Validate the token
        if not representative.is_valid():
            return Response({"detail": "Invitation has expired"}, status=status.HTTP_400_BAD_REQUEST)
        
        if not representative.verify_token(token):
            return Response({"detail": "Invalid token"}, status=status.HTTP_400_BAD_REQUEST)

        representative.status = True
        representative.invited = timezone.now()
        representative.save()

        return Response({"message": "Invitation accepted successfully", "success": True}, status=status.HTTP_200_OK)
