from django.contrib.postgres.search import (
    SearchVector, SearchQuery, SearchRank, TrigramSimilarity
)
from django.db.models import Q, F
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework.pagination import PageNumberPagination

from api.models import Company, Post, Product, Service
from api.serializers.all_serializer import (
    CompanySerializer,
    PostSerializer,
    ProductSerializer,
    ServiceSerializer,
)
from authentication.models import User


class AdvancedSearchPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100


class AdvancedSearchView(APIView):
    """
    Advanced search view using PostgreSQL full-text search and trigram similarity.
    Provides better performance and relevance ranking for search results.
    """
    permission_classes = [AllowAny]
    pagination_class = AdvancedSearchPagination

    def get(self, request, format=None):
        query = request.query_params.get('q', '').strip()
        search_type = request.query_params.get('type', 'all')  # all, companies, products, services, posts
        min_similarity = float(request.query_params.get('min_similarity', 0.1))
        
        if not query:
            return Response(
                {"error": "A query parameter 'q' is required."}, 
                status=400
            )

        results = {}
        
        if search_type in ['all', 'companies']:
            results['companies'] = self._search_companies(query, min_similarity)
            
        if search_type in ['all', 'products']:
            results['products'] = self._search_products(query, min_similarity)
            
        if search_type in ['all', 'services']:
            results['services'] = self._search_services(query, min_similarity)
            
        if search_type in ['all', 'posts']:
            results['posts'] = self._search_posts(query, min_similarity)

        return Response({
            "query": query,
            "search_type": search_type,
            "min_similarity": min_similarity,
            "results": results
        })

    def _search_companies(self, query, min_similarity=0.1):
        """Search companies using full-text search and trigram similarity."""
        search_vector = SearchVector(
            'company_name', weight='A', config='english'
        ) + SearchVector(
            'about', weight='B', config='english'
        ) + SearchVector(
            'tag_line', weight='B', config='english'
        ) + SearchVector(
            'country', 'city', 'office_address', weight='C', config='english'
        )
        
        search_query = SearchQuery(query, config='english')
        
        companies = Company.objects.annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            similarity=TrigramSimilarity('company_name', query)
        ).filter(
            Q(search=search_query) | Q(similarity__gt=min_similarity)
        ).select_related(
            'organization_type', 'company_size'
        ).order_by('-rank', '-similarity')[:20]

        serializer = CompanySerializer(companies, many=True)
        return {
            "count": len(companies),
            "data": serializer.data
        }

    def _search_products(self, query, min_similarity=0.1):
        """Search products using full-text search and trigram similarity."""
        search_vector = SearchVector(
            'title', weight='A', config='english'
        ) + SearchVector(
            'sub_title', weight='B', config='english'
        ) + SearchVector(
            'description', weight='C', config='english'
        )
        
        search_query = SearchQuery(query, config='english')
        
        products = Product.objects.annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            title_similarity=TrigramSimilarity('title', query)
        ).filter(
            Q(search=search_query) | Q(title_similarity__gt=min_similarity)
        ).select_related(
            'category', 'company'
        ).order_by('-rank', '-title_similarity')[:20]

        serializer = ProductSerializer(products, many=True)
        return {
            "count": len(products),
            "data": serializer.data
        }

    def _search_services(self, query, min_similarity=0.1):
        """Search services using full-text search and trigram similarity."""
        search_vector = SearchVector(
            'title', weight='A', config='english'
        ) + SearchVector(
            'sub_title', weight='B', config='english'
        ) + SearchVector(
            'description', weight='C', config='english'
        )
        
        search_query = SearchQuery(query, config='english')
        
        services = Service.objects.annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            title_similarity=TrigramSimilarity('title', query)
        ).filter(
            Q(search=search_query) | Q(title_similarity__gt=min_similarity)
        ).select_related(
            'category', 'company'
        ).order_by('-rank', '-title_similarity')[:20]

        serializer = ServiceSerializer(services, many=True)
        return {
            "count": len(services),
            "data": serializer.data
        }

    def _search_posts(self, query, min_similarity=0.1):
        """Search posts using full-text search and trigram similarity."""
        search_vector = SearchVector('body', weight='A', config='english')
        search_query = SearchQuery(query, config='english')
        
        posts = Post.objects.annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query),
            body_similarity=TrigramSimilarity('body', query)
        ).filter(
            Q(search=search_query) | Q(body_similarity__gt=min_similarity)
        ).select_related(
            'company', 'user'
        ).order_by('-rank', '-body_similarity')[:20]

        serializer = PostSerializer(posts, many=True)
        return {
            "count": len(posts),
            "data": serializer.data
        }


class AutocompleteView(APIView):
    """
    Fast autocomplete endpoint using trigram similarity.
    Optimized for real-time search suggestions.
    """
    permission_classes = [AllowAny]

    def get(self, request, format=None):
        query = request.query_params.get('q', '').strip()
        limit = min(int(request.query_params.get('limit', 10)), 20)
        
        if len(query) < 2:
            return Response({"suggestions": []})

        # Get company name suggestions
        company_suggestions = Company.objects.annotate(
            similarity=TrigramSimilarity('company_name', query)
        ).filter(
            similarity__gt=0.2
        ).order_by('-similarity').values_list(
            'company_name', flat=True
        )[:limit//2]

        # Get product title suggestions
        product_suggestions = Product.objects.annotate(
            similarity=TrigramSimilarity('title', query)
        ).filter(
            similarity__gt=0.2
        ).order_by('-similarity').values_list(
            'title', flat=True
        )[:limit//2]

        suggestions = list(company_suggestions) + list(product_suggestions)
        
        return Response({
            "query": query,
            "suggestions": suggestions[:limit]
        })
