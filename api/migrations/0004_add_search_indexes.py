# Generated manually for search optimization

from django.db import migrations, models
from django.contrib.postgres.operations import TrigramExtension


class Migration(migrations.Migration):

    dependencies = [
        ("api", "0003_productimage_created_at_productimage_temporary"),
    ]

    operations = [
        # Enable PostgreSQL trigram extension for better text search
        TrigramExtension(),
        
        # Company search indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS api_company_company_name_gin ON api_company USING gin(company_name gin_trgm_ops);",
            reverse_sql="DROP INDEX IF EXISTS api_company_company_name_gin;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS api_company_about_gin ON api_company USING gin(about gin_trgm_ops);",
            reverse_sql="DROP INDEX IF EXISTS api_company_about_gin;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS api_company_location_gin ON api_company USING gin((country || ' ' || city || ' ' || office_address) gin_trgm_ops);",
            reverse_sql="DROP INDEX IF EXISTS api_company_location_gin;"
        ),
        
        # Product search indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS api_product_title_gin ON api_product USING gin(title gin_trgm_ops);",
            reverse_sql="DROP INDEX IF EXISTS api_product_title_gin;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS api_product_description_gin ON api_product USING gin(description gin_trgm_ops);",
            reverse_sql="DROP INDEX IF EXISTS api_product_description_gin;"
        ),
        
        # Service search indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS api_service_title_gin ON api_service USING gin(title gin_trgm_ops);",
            reverse_sql="DROP INDEX IF EXISTS api_service_title_gin;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS api_service_description_gin ON api_service USING gin(description gin_trgm_ops);",
            reverse_sql="DROP INDEX IF EXISTS api_service_description_gin;"
        ),
        
        # Post search indexes
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS api_post_body_gin ON api_post USING gin(body gin_trgm_ops);",
            reverse_sql="DROP INDEX IF EXISTS api_post_body_gin;"
        ),
        
        # Compound indexes for common search patterns
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS api_product_company_category ON api_product(company_id, category_id);",
            reverse_sql="DROP INDEX IF EXISTS api_product_company_category;"
        ),
        migrations.RunSQL(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS api_service_company_category ON api_service(company_id, category_id);",
            reverse_sql="DROP INDEX IF EXISTS api_service_company_category;"
        ),
    ]
