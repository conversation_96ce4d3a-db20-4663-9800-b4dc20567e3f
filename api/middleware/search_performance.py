import time
import logging
from django.db import connection
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger('search_performance')


class SearchPerformanceMiddleware(MiddlewareMixin):
    """
    Middleware to monitor search performance and log slow queries.
    """
    
    def process_request(self, request):
        if '/search' in request.path:
            request.search_start_time = time.time()
            request.search_queries_before = len(connection.queries)
    
    def process_response(self, request, response):
        if hasattr(request, 'search_start_time'):
            duration = time.time() - request.search_start_time
            query_count = len(connection.queries) - request.search_queries_before
            
            # Log performance metrics
            logger.info(
                f"Search Performance - Path: {request.path}, "
                f"Duration: {duration:.3f}s, "
                f"Queries: {query_count}, "
                f"Query: {request.GET.get('q', 'N/A')}"
            )
            
            # Log slow searches
            if duration > 1.0:  # Log searches taking more than 1 second
                logger.warning(
                    f"Slow Search - Path: {request.path}, "
                    f"Duration: {duration:.3f}s, "
                    f"Queries: {query_count}, "
                    f"Query: {request.GET.get('q', 'N/A')}"
                )
                
                # Log actual SQL queries for debugging
                if query_count > 10:  # Log if too many queries
                    recent_queries = connection.queries[-query_count:]
                    for i, query in enumerate(recent_queries):
                        logger.debug(f"Query {i+1}: {query['sql'][:200]}...")
        
        return response
