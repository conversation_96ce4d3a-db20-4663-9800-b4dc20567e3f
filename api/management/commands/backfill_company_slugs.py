from django.core.management.base import BaseCommand
from django.utils.text import slugify
from django.db.models import Q

from api.models.company_model import Company


class Command(BaseCommand):
    help = "Backfill slug field for companies that do not have one"

    def handle(self, *args, **kwargs):
        companies = Company.objects.filter(Q(slug__isnull=True) | Q(slug=""))
        total = companies.count()
        updated = 0

        for company in companies:
            base_slug = slugify(company.company_name)
            slug = base_slug
            counter = 1

            while Company.objects.filter(~Q(pk=company.pk), slug=slug).exists():
                slug = f"{base_slug}-{counter}"
                counter += 1

            company.slug = slug
            company.save()
            updated += 1
            self.stdout.write(f"✔ Slug set for: {company.company_name} → {slug}")

        self.stdout.write(
            self.style.SUCCESS(
                f"\n✅ Backfilled slugs for {updated}/{total} companies."
            )
        )