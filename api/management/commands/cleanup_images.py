"""
Django management command for cleaning up temporary and orphaned image files.
"""

from datetime import timedelta

from django.core.management.base import BaseCommand
from django.utils import timezone

from api.models.product_model import ProductImage
from api.utils.file_cleanup import cleanup_orphaned_files, get_storage_info


class Command(BaseCommand):
    help = 'Clean up temporary and orphaned image files'

    def add_arguments(self, parser):
        parser.add_argument(
            '--hours',
            type=int,
            default=24,
            help='Delete temporary images older than this many hours (default: 24)'
        )
        parser.add_argument(
            '--orphaned',
            action='store_true',
            help='Also clean up orphaned files in storage'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting'
        )

    def handle(self, *args, **options):
        hours = options['hours']
        include_orphaned = options['orphaned']
        dry_run = options['dry_run']

        self.stdout.write(f"Image cleanup started (dry_run={dry_run})")

        # Show storage info
        storage_info = get_storage_info()
        self.stdout.write(f"Storage backend: {storage_info['backend']}")
        if 'bucket_name' in storage_info:
            self.stdout.write(f"S3 bucket: {storage_info['bucket_name']}")

        # Clean up temporary images
        cutoff_time = timezone.now() - timedelta(hours=hours)
        temp_images = ProductImage.objects.filter(
            temporary=True,
            product__isnull=True,
            created_at__lt=cutoff_time
        )

        temp_count = temp_images.count()
        self.stdout.write(f"Found {temp_count} temporary images older than {hours} hours")

        if temp_count > 0:
            if dry_run:
                self.stdout.write("Would delete the following temporary images:")
                for img in temp_images[:10]:  # Show first 10
                    self.stdout.write(f"  - {img.image.name} (created: {img.created_at})")
                if temp_count > 10:
                    self.stdout.write(f"  ... and {temp_count - 10} more")
            else:
                deleted_count = temp_images.delete()[0]
                self.stdout.write(
                    self.style.SUCCESS(f"Deleted {deleted_count} temporary images")
                )

        # Clean up orphaned files if requested
        if include_orphaned:
            self.stdout.write("Checking for orphaned files...")
            if dry_run:
                self.stdout.write("Orphaned file cleanup not available in dry-run mode")
            else:
                files_found, files_deleted = cleanup_orphaned_files()
                self.stdout.write(
                    f"Found {files_found} files, deleted {files_deleted} orphaned files"
                )

        self.stdout.write(self.style.SUCCESS("Image cleanup completed"))
