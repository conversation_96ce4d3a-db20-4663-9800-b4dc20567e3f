from django.db import models
from authentication.models import User
from api.models.company_model import Company
from django.utils import timezone
from django.utils.text import slugify
from django.utils.crypto import get_random_string
from django.utils.timezone import now
from django.contrib.auth.hashers import make_password, check_password


class RepresentativeCategory(models.Model):
    type = models.CharField(max_length=500, null=True)

    def __str__(self):
        return self.type

    class Meta:
        verbose_name_plural = "Representative Categories"
        ordering = ['type']


class Representative(models.Model):
    user = models.ForeignKey(
        User,
        on_delete=models.DO_NOTHING,
        related_name="representatives",
        null=True,
        blank=True
    )
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, related_name="representatives", null=True, blank=True)
    category = models.ForeignKey(RepresentativeCategory, on_delete=models.DO_NOTHING, null=True, blank=True)
    token = models.CharField(max_length=255, null=True, blank=True)
    status = models.BooleanField(default=False, db_index=True)
    invited = models.DateTimeField(null=True, blank=True, db_index=True) 
    date_created = models.DateTimeField(default=timezone.now) 
    date_updated = models.DateTimeField(auto_now=True)  # can act as date accepted
    slug = models.SlugField(blank=True, null=True, max_length=500)
    expires_at = models.DateTimeField(blank=True, null=True)

    def save(self, *args, **kwargs):
        if not self.token:
            plain_token = get_random_string(64)  # Save plain token temporarily
            self.token = make_password(plain_token)  # Generate a unique token
        if not self.expires_at:
            self.expires_at = now() + timezone.timedelta(days=1)  # Set 1-day expiry
        if not self.slug and self.user:
            self.slug = slugify(self.user, allow_unicode=True)
        super().save(*args, **kwargs)
        
    def verify_token(self, plain_token):
        return check_password(plain_token, self.token)

    def is_valid(self):
        if self.status:
            return False
        if now() > self.expires_at:
            return False
        return True
    
    class Meta:
        unique_together = (('user', 'company'),)

