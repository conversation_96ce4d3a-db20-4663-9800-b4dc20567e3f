from django.db import models
from authentication.models import User
from wagtail.fields import RichTextField
from django.utils.text import slugify
from django.utils import timezone
from django.db.models import Q
# Create your models here.


class CompanyCategory(models.Model):
    name = models.CharField(max_length=500, null=True, blank=True, unique=True)
    slug = models.SlugField(null=True, max_length=500)

    def save(self, *args, **kwargs):
        self.slug = slugify(self.name, allow_unicode=True)
        super(CompanyCategory, self).save(*args, **kwargs)

    def __str__(self):
        return f"{self.name}"
    class Meta:
        verbose_name_plural = "Company Categories"
        ordering = ['name']


class CompanySize(models.Model):
    size = models.CharField(max_length=500, null=True, blank=True, unique=True)
    slug = models.SlugField(null=True, max_length=500)

    def save(self, *args, **kwargs):
        self.slug = slugify(self.size, allow_unicode=True)
        super(CompanySize, self).save(*args, **kwargs)

    def __str__(self):
        return f"{self.size}"
    class Meta:
        verbose_name_plural = "Company Sizes"
        ordering = ['size']


class Company(models.Model):
    profile = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="companies")
    company_name = models.CharField(max_length=500, null=True, blank=True, unique=True)
    registration_number = models.CharField(max_length=255, null=True, blank=True)
    annual_revenue = models.CharField(max_length=500, null=True, blank=True)
    organization_type = models.ForeignKey(CompanyCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name="companies")
    about = RichTextField(null=True, blank=True)
    tag_line = models.CharField(max_length=500, null=True, blank=True)
    company_size = models.ForeignKey(CompanySize, on_delete=models.DO_NOTHING, null=True, related_name="companies")
    logo = models.ImageField(null=True, blank=True)
    banner = models.ImageField(blank=True, null=True)
    email = models.EmailField(null=True, blank=True)
    office_address = models.CharField(max_length=500, null=True, blank=True)
    country = models.CharField(max_length=500, null=True, blank=True)
    state = models.CharField(max_length=500, null=True, blank=True)
    city = models.CharField(max_length=500, null=True, blank=True)
    website = models.URLField(null=True, blank=True)
    verify = models.BooleanField(default=False)
    slug = models.SlugField(null=True, max_length=500, unique=True, blank=True)
    registration_date = models.DateField(null=True)
    date_created = models.DateTimeField(default=timezone.now)
    date_updated = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.slug and self.company_name:
            base_slug = slugify(self.company_name)
            slug = base_slug
            counter = 1
            while Company.objects.filter(~Q(pk=self.pk), slug=slug).exists():
                slug = f"{base_slug}-{counter}"
                counter += 1
            self.slug = slug
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.company_name}"
    
    class Meta:
        verbose_name_plural = "Companies"
        ordering = ["-date_created"]
    @property
    def organization_type_indexing(self):
        """Organization type for indexing.

        Used in Elasticsearch indexing.
        """
        if self.organization_type is not None:
            return self.organization_type.name

    @property
    def profile_indexing(self):
        """Profile type for indexing.

        Used in Elasticsearch indexing.
        """
        if self.profile is not None:
            return self.profile.full_name
