import logging

from django.core.files.storage import default_storage
from django.db import models
from django.db.models.signals import post_delete, pre_delete
from django.dispatch import receiver
from django.utils import timezone
from django.utils.text import slugify
from wagtail.fields import RichTextField

from api.models.company_model import Company

logger = logging.getLogger(__name__)


class ProductCategory(models.Model):
    name = models.CharField(max_length=500, null=True, blank=True, unique=True)
    slug = models.SlugField(null=True, max_length=500)

    def save(self, *args, **kwargs):
        self.slug = slugify(self.name, allow_unicode=True)
        super(ProductCategory, self).save(*args, **kwargs)

    def __str__(self):
        return f"{self.name}"
    
    class Meta:
        verbose_name_plural = "Product Categories"
        ordering = ['name']
    

class Product(models.Model):
    title = models.CharField(max_length=500, null=True, blank=True)
    sub_title = models.CharField(max_length=500, null=True, blank=True)
    category = models.ForeignKey(ProductCategory, on_delete=models.DO_NOTHING, null=True, related_name='products')
    description = RichTextField()
    date_created = models.DateTimeField(default=timezone.now)
    date_updated = models.DateTimeField(auto_now=True)
    featured = models.BooleanField(default=False)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, related_name='products')
    slug = models.SlugField(null=True, max_length=500)

    def save(self, *args, **kwargs):
        self.slug = slugify(self.title, allow_unicode=True)
        super(Product, self).save(*args, **kwargs)

    def __str__(self):
        return f"{self.title}, {self.category}"

    @property
    def category_indexing(self):
        """Profile type for indexing.

        Used in Elasticsearch indexing.
        """
        if self.category is not None:
            return self.category.name

    @property
    def company_indexing(self):
        """Profile type for indexing.

        Used in Elasticsearch indexing.
        """
        if self.company is not None:
            return self.company.company_name
        
    class Meta:
        ordering = ["-date_created"]
        verbose_name_plural = "Products"
        verbose_name = "Product"


class ProductImage(models.Model):
    caption = models.CharField(max_length=500, null=True, blank=True)
    image = models.ImageField(upload_to='product/')
    product = models.ForeignKey(Product, on_delete=models.DO_NOTHING, null=True, related_name='images')
    temporary = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        order_with_respect_to = 'product'

    def delete_image_file(self):
        """
        Delete the actual image file from storage.
        Works with both local filesystem and S3 storage.
        """
        if self.image:
            try:
                if default_storage.exists(self.image.name):
                    default_storage.delete(self.image.name)
                    logger.info(f"Successfully deleted image file: {self.image.name}")
                else:
                    logger.warning(f"Image file not found in storage: {self.image.name}")
            except Exception as e:
                logger.error(f"Failed to delete image file {self.image.name}: {str(e)}")


# Signal handlers for automatic file cleanup
@receiver(post_delete, sender=ProductImage)
def delete_product_image_file(sender, instance, **kwargs):
    """
    Delete the image file when a ProductImage instance is deleted.
    """
    instance.delete_image_file()


@receiver(pre_delete, sender=Product)
def delete_product_images_files(sender, instance, **kwargs):
    """
    Delete all associated image files when a Product is deleted.
    """
    for image in instance.images.all():
        image.delete_image_file()
