from rest_framework import permissions


class IsCompanyOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of a company to edit/delete their resources.
    Read permissions are allowed for any authenticated user.
    """

    def has_permission(self, request, view):
        # Read permissions for authenticated users
        if request.method in permissions.SAFE_METHODS:
            return request.user and request.user.is_authenticated

        # Write permissions require authentication
        return request.user and request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # Read permissions for authenticated users
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions only for company owners
        if hasattr(obj, 'company') and obj.company:
            return obj.company.profile == request.user

        return False


class IsCompanyOwner(permissions.BasePermission):
    """
    Custom permission to only allow owners of a company to access their resources.
    Stricter than IsCompanyOwnerOrReadOnly - requires ownership for all operations.
    """

    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # All operations require company ownership
        if hasattr(obj, 'company') and obj.company:
            return obj.company.profile == request.user

        return False


class IsAuthenticatedOrReadOnly(permissions.BasePermission):
    """
    Custom permission to allow read access to anyone but require authentication for writes.
    """

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        return request.user and request.user.is_authenticated