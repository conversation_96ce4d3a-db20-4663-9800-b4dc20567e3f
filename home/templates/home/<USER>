{% extends "base.html" %} {% load static %}{% load wagtailimages_tags %}{% load wagtailcore_tags %}{% block content %}

<section class="home-hero">
    {% include 'header.html' %}
    <div class="row welcome-text">
        <div class="row justify-content-center">
            <h1 class="h1 welcome-cta-h1 wow animate__animated animate__slideInRight" loading="lazy">
                <span>The Goal </span> is for <span><b class="word"></b></span> to Connect.
                <script>
                    var words = ['Companies', 'Businesses'],
                    part,
                    i = 0,
                    offset = 0,
                    len = words.length,
                    forwards = true,
                    skip_count = 0,
                    skip_delay = 25,
                    speed = 180;
                    var wordflick = function () {
                    setInterval(function () {
                        if (forwards) {
                        if (offset >= words[i].length) {
                            ++skip_count;
                            if (skip_count == skip_delay) {
                            forwards = false;
                            skip_count = 0;
                            }
                        }
                        }
                        else {
                        if (offset == 0) {
                            forwards = true;
                            i++;
                            offset = 0;
                            if (i >= len) {
                            i = 0;
                            }
                        }
                        }
                        part = words[i].substr(0, offset);
                        if (skip_count == 0) {
                        if (forwards) {
                            offset++;
                        }
                        else {
                            offset--;
                        }
                        }
                        $('.word').text(part);
                    },speed);
                    };

                    $(document).ready(function () {
                    wordflick();
                    });
                </script>
            </h1>
        </div>
        <div class="row justify-content-center wow animate__animated animate__slideInUp" loading="lazy">
            <p class="welcome-cta-p">
                Discover and engage with key stakeholders within Nigeria's dynamic oil and gas sector. Connectize facilitates seamless networking opportunities for international and local oil and gas enterprises. Connect with carefully selected industry representatives and facilitate successful business transactions.
            </p>
        </div>
    </div>
    <div class="justify-content-center hero-cta-btn-container">
        <div class="col col-md-auto"><a class="btn btn-primary join-waitlist-btn" role="button" href="#join-waitlist">Join Now</a></div>
        <div class="col col-md-auto">
            <a class="btn btn-primary join-waitlist-btn no-background" role="button" href="{{ settings.waitlist.ImportantPages.page_url.about }}">Learn More</a></div>
    </div>
</section>

<section class="container product-mockup">
    <img class="people-group" src="/static/images/Group 2411.png" alt="">
    <div class="first-mockup">
        <img class="laptop" src="/static/images/connectizelaptopmock.png" alt="">
        <img class="phone wow animate__animated animate__wobble" src="/static/images/connectphonemockup.png" alt="">
    </div>
</section>
<!-- <section class="trusted-by container animate__animated animate__slideInLeft" loading="lazy">
    <div class="row">
        <div class="col-md-4 trusted-by-text-div">TRUSTED by</div>
        <div class="col-md-8">
            <div class="row">
                <div class="col trusted-companies-img"><img src="/static/images/affiliateLogo-2x 1.png" alt=""></div>
                <div class="col trusted-companies-img"><img src="/static/images/exxonmobillogocolor2x 1.png" alt=""></div>
                <div class="col trusted-companies-img"><img src="/static/images/logo-svg 1.png" alt=""></div>
                <div class="col trusted-companies-img"><img src="/static/images/nigerian-national-petroleum-company-seeklogo.com 1.png" alt=""></div>
                <div class="col trusted-companies-img"><img src="/static/images/xtologocolor2x 1.png" alt=""></div>
            </div>
        </div>
    </div>
</section> -->
<section class="about-summary container">
    <div class="section-header align-items-center">
        <div class="section-title-1 wow animate__animated animate__slideInRight" loading="lazy">
            <h1>If your customers are companies, <span>Join Connectize.</span></h1>
        </div>
        <div class="section-title-2">
            <a href="{{ settings.waitlist.ImportantPages.page_url.about }}" class="learn-more-link">Learn More</a>
        </div>
    </div>
    <div class="row about-summary-image-and-cards">
        <div class="row about-summary-cards">
            <div class="col about-summary-card card-1 wow animate__animated animate__slideInLeft" loading="lazy">
                <span class="subtitle show">Accessibility</span>
                 <h2 class="show">Don't let accessibility stop you from selling</h2>
                <p class="hide-text">Connectize lets you find and talk to the right people in any company. Meet with handpicked representatives who can help you close the deal.
                </p>
            </div>
            <div class="col about-summary-card card-3 wow animate__animated animate__slideInLeft " loading="lazy ">
                <span class="subtitle show">Visibility</span>
                <h2 class="show">Showcase your brand, services, and products to the world.</h2>
                <p class="hide-text">Connectize helps you create a stunning profile that attracts attention. Stand out from the crowd and impress your potential clients.
                </p>
            </div>
            <div class="col about-summary-card card-1 wow animate__animated animate__slideInLeft" loading="lazy">
                <span class="subtitle show">Connectivity</span>
                <h2 class="show">Stay in touch with your network and keep them updated.</h2>
                <p class="hide-text">Drop a news and get engagement on what is happening in your company. Make announcements and initiate marketing programs that generate leads and conversions.</p>
            </div>
            <div class="col about-summary-card card-3 wow animate__animated animate__slideInLeft " loading="lazy ">
                <span class="subtitle show">Credibility</span>
                <h2 class="show">Connect with verified companies, and avoid scams</h2>
                <p class="hide-text">Connect with verified companies, and enhance your credibility score through our carefully design rating and verification system.
                </p>
            </div>
        </div>
    </div>
</section>
<section class="features-services container">
    <div class="section-header row align-items-center">
        <div class="section-title-1 col wow animate__animated animate__slideInRight" loading="lazy">
            <h1>Come to the <span>Limelight</span></h1>
            <p>You cannot wink in the dark and expect to be seen. Create a profile and a company page to showcase your offerings and attract potential customers.</p>
        </div>
        <div class="section-title-2 col">
            <a href="{{ settings.waitlist.ImportantPages.page_url.about }} " class="learn-more-link btn btn-primary join-waitlist-btn learn-more-button " role="button ">Learn More</a>
        </div>
    </div>

</section>
<section class="features-services container">
    <img src="/static/images/News feed.jpg" class="img-fluid wow animate__animated animate__slideInUp" alt=" ">
    <div class="row service-feature-container justify-content-center">
        <div class="col service-feature-cards margin-right">
            <div class="service-feature-card service-feature-card-1 wow animate__animated animate__slideInUp">
                <svg xmlns="http://www.w3.org/2000/svg " width="88" height="88" viewBox="0 0 88 88" fill="none">
                    <g clip-path="url(#clip0_293_6298) ">
                      <path d="M44 3.66675L11 18.3334V40.3334C11 60.6834 25.08 79.7134 44 84.3334C62.92 79.7134 77 60.6834 77 40.3334V18.3334L44 3.66675ZM44 43.9634H69.6667C67.7233 59.0701 57.64 72.5267 44 76.7434V44.0001H18.3333V23.1001L44 11.6967V43.9634Z
                " fill="#F1C644"/>
                    </g>
                    <defs>
                      <clipPath id="clip0_293_6298 ">
                        <rect width="88 " height="88 " fill="white"/>
                      </clipPath>
                    </defs>
                </svg>
                <div>
                    <h2>Communicate and Collaborate
                    </h2>
                    <p>You can exchange messages, files, and feedback in real-time and build trust and rapport.</p>
                </div>
                
            </div>
            <div class="service-feature-card service-feature-card-2 wow animate__animated animate__slideInUp">
                <svg xmlns="http://www.w3.org/2000/svg " width="130 " height="117 " viewBox="0 0 130 117 " fill="none ">
                    <path d="M84.5 35.1C84.5 44.7926 75.7696 52.65 65 52.65C54.2304 52.65 45.5 44.7926 45.5 35.1C45.5 25.4075 54.2304 17.55 65 17.55C75.7696 17.55 84.5 25.4075 84.5 35.1Z " fill="#F1C644"/>
                    <path d="M117 46.8C117 53.2618 111.18 58.5 104 58.5C96.8203 58.5 91 53.2618 91 46.8C91 40.3383 96.8203 35.1 104 35.1C111.18 35.1 117 40.3383 117 46.8Z " fill="#F1C644"/>
                    <path d="M91 87.75C91 74.8266 79.3594 64.35 65 64.35C50.6406 64.35 39 74.8266 39 87.75V105.3H91V87.75Z " fill="#F1C644"/>
                    <path d="M39 46.8C39 53.2618 33.1797 58.5 26 58.5C18.8203 58.5 13 53.2618 13 46.8C13 40.3383 18.8203 35.1 26 35.1C33.1797 35.1 39 40.3383 39 46.8Z " fill="#F1C644"/>
                    <path d="M104 105.3V87.75C104 81.5833 102.233 75.788 99.1298 70.7518C100.687 70.3916 102.318 70.2 104 70.2C114.77 70.2 123.5 78.0574 123.5 87.75V105.3H104Z " fill="#F1C644"/>
                    <path d="M30.8702 70.7518C27.767 75.788 26 81.5833 26 87.75V105.3H6.5V87.75C6.5 78.0574 15.2304 70.2 26 70.2C27.6816 70.2 29.3135 70.3916 30.8702 70.7518Z " fill="#F1C644"/>
                </svg>
                <div>
                    <h2>Rate and Review</h2>
                    <p>Connectize values your opinion and feedback. You can rate and review any company, product, or service you have interacted with on the platform.</p>
                </div>  
            </div>
        </div>        
        <div class="col service-feature-cards shift-down">
            <div class="service-feature-card service-feature-card-1 wow animate__animated animate__slideInUp">
                <svg xmlns="http://www.w3.org/2000/svg " width="85 " height="85 " viewBox="0 0 85 85 " fill="none ">
                    <path d="M20.5001 7.08325C24.3015 7.08325 27.9473 8.57581 30.6353 11.2326C33.3233 13.8893 34.8334 17.4927 34.8334 21.2499V28.3333H49.1667V21.2499C49.1667 17.4927 50.6769 13.8893 53.3649 11.2326C56.0529 8.57581 59.6986 7.08325 63.5001
                7.08325C67.3015 7.08325 70.9473 8.57581 73.6353 11.2326C76.3233 13.8893 77.8334 17.4927 77.8334 21.2499C77.8334 25.0072 76.3233 28.6105 73.6353 31.2673C70.9473 33.924 67.3015 35.4166 63.5001 35.4166H56.3334V49.5833H63.5001C67.3015 49.5833
                70.9473 51.0758 73.6353 53.7326C76.3233 56.3893 77.8334 59.9927 77.8334 63.7499C77.8334 67.5072 76.3233 71.1105 73.6353 73.7673C70.9473 76.424 67.3015 77.9166 63.5001 77.9166C59.6986 77.9166 56.0529 76.424 53.3649 73.7673C50.6769 71.1105 49.1668
                67.5072 49.1667 63.7499V56.6666H34.8334V63.7499C34.8334 67.5072 33.3233 71.1105 30.6353 73.7673C27.9473 76.424 24.3015 77.9166 20.5001 77.9166C16.6986 77.9166 13.0529 76.424 10.3649 73.7673C7.67686 71.1105 6.16675 67.5072 6.16675 63.7499C6.16675
                59.9927 7.67686 56.3893 10.3649 53.7326C13.0529 51.0758 16.6986 49.5833 20.5001 49.5833H27.6667V35.4166H20.5001C16.6986 35.4166 13.0529 33.924 10.3649 31.2673C7.67686 28.6105 6.16675 25.0072 6.16675 21.2499C6.16675 17.4927 7.67686 13.8893
                10.3649 11.2326C13.0529 8.57581 16.6986 7.08325 20.5001 7.08325ZM56.3334 63.7499C56.3334 65.6285 57.0885 67.4302 58.4325 68.7586C59.7765 70.087 61.5994 70.8333 63.5001 70.8333C65.4008 70.8333 67.2237 70.087 68.5677 68.7586C69.9117 67.4302
                70.6667 65.6285 70.6667 63.7499C70.6667 61.8713 69.9117 60.0696 68.5677 58.7412C67.2237 57.4129 65.4008 56.6666 63.5001 56.6666H56.3334V63.7499ZM49.1667 35.4166H34.8334V49.5833H49.1667V35.4166ZM20.5001 56.6666C18.5994 56.6666 16.7765 57.4129
                15.4325 58.7412C14.0885 60.0696 13.3334 61.8713 13.3334 63.7499C13.3334 65.6285 14.0885 67.4302 15.4325 68.7586C16.7765 70.087 18.5994 70.8333 20.5001 70.8333C22.4008 70.8333 24.2237 70.087 25.5677 68.7586C26.9117 67.4302 27.6667 65.6285 27.6667
                63.7499V56.6666H20.5001ZM27.6667 21.2499C27.6667 19.3713 26.9117 17.5696 25.5677 16.2412C24.2237 14.9129 22.4008 14.1666 20.5001 14.1666C18.5994 14.1666 16.7765 14.9129 15.4325 16.2412C14.0885 17.5696 13.3334 19.3713 13.3334 21.2499C13.3334
                23.1285 14.0885 24.9302 15.4325 26.2586C16.7765 27.587 18.5994 28.3333 20.5001 28.3333H27.6667V21.2499ZM63.5001 28.3333C65.4008 28.3333 67.2237 27.587 68.5677 26.2586C69.9117 24.9302 70.6667 23.1285 70.6667 21.2499C70.6667 19.3713 69.9117
                17.5696 68.5677 16.2412C67.2237 14.9129 65.4008 14.1666 63.5001 14.1666C61.5994 14.1666 59.7765 14.9129 58.4325 16.2412C57.0885 17.5696 56.3334 19.3713 56.3334 21.2499V28.3333H63.5001Z " fill="#F1C644"/>
                </svg>
                <div>
                    <h2>Stay Updated and Informed</h2>
                    <p>Connectize keeps you updated and informed about the latest news and trends in your industry and market.</p>
                </div>
                
            </div>
            <div class="service-feature-card service-feature-card-2 wow animate__animated animate__slideInUp">
                <svg xmlns="http://www.w3.org/2000/svg " width="88 " height="86 " viewBox="0 0 88 86 " fill="none">
                    <path fill-rule="evenodd " clip-rule="evenodd " d="M22 12.8999C17.1398 12.8999 13.2 16.7503 13.2 21.4999V64.4999C13.2 69.2495 17.1398 73.0999 22 73.0999H66C70.8601 73.0999 74.8 69.2495 74.8 64.4999V21.4999C74.8 16.7503 70.8601 12.8999
                66 12.8999H22ZM61.6 30.0999C61.6 27.7251 59.63 25.7999 57.2 25.7999C54.7699 25.7999 52.8 27.7251 52.8 30.0999V55.8999C52.8 58.2747 54.7699 60.1999 57.2 60.1999C59.63 60.1999 61.6 58.2747 61.6 55.8999V30.0999ZM48.4 38.6999C48.4 36.3251 46.43
                34.3999 44 34.3999C41.5699 34.3999 39.6 36.3251 39.6 38.6999V55.8999C39.6 58.2747 41.5699 60.1999 44 60.1999C46.43 60.1999 48.4 58.2747 48.4 55.8999V38.6999ZM35.2 51.5999C35.2 49.2251 33.23 47.2999 30.8 47.2999C28.3699 47.2999 26.4 49.2251
                26.4 51.5999V55.8999C26.4 58.2747 28.3699 60.1999 30.8 60.1999C33.23 60.1999 35.2 58.2747 35.2 55.8999V51.5999Z " fill="#F1C644"/>
                </svg>
                <div>
                    <h2>Global Reach, Local Impact</h2>
                    <p>Connectize is more than a platform, it is a community of companies who interact and collaborate with each other.</p>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="site-metrics container">
    <div class="section-header row align-items-start ">
        <div class="section-title-1 col wow animate__animated animate__slideInUp">
            <h1>Unifying <span>Oil and Gas</span> Community.</h1>
            <h6>The ultimate platform for foreign companies to meet with the thriving Nigerian industry.</h6>
            <p>Are you a Foreign or Nigerian Company seeking visibility in the Nigerian Oil and Gas Industry, Join Connectize and discover limitless opportunities, access and most especially visibility. Connectize is set to revolutionize the way businesses connect and collaborate. Join now and enjoy 80% less hassle and more opportunities.
            </p>
        </div>
        <!-- <div class="section-title-2 col ">
            <div class="metrics-card col animate__animated animate__slideInRight ">
                <h2>100K+</h2>
                <p>Facilities</p>
            </div>
            <div class="metrics-card col animate__animated animate__slideInRight ">
                <h2>50K</h2>
                <p>Collaborators</p>
            </div>
            <div class="metrics-card colanimate__animated animate__slideInRight ">
                <h2>1M+</h2>
                <p>Users</p>
            </div>
        </div> -->
        <img src="/static/images/Home_Phone___Landline_Service_Provider___Spectrum-removebg-preview 1.png" class="img-fluid map-image" alt=" ">
    </div>
</section>

{% if testimonials %}
<section class="about-summary container ">
    <div class="section-header row align-items-center wow animate__animated animate__slideInUp">
        <div class="section-title-1 col ">
            <h1>Testimonials</h1>
        </div>
        <div class="section-title-2 col ">
            <a href="#join-waitlist " class="learn-more-link btn btn-primary join-waitlist-btn learn-more-button " role="button ">Join Now</a>
        </div>
    </div>

</section>

<section class="row testimonial-container container-fluid" loading="lazy">
    {% if testimonials %}{% for person in testimonials %}
    <div class="tesmonial-card wow animate__animated animate__slideInUp">
        <div class="row tesmonial-card-sub">
            <div class="col-4 testimony-avatar">
                <img src="{{person.avatar.utl}}" alt=" ">
            </div>
            <div class="col-8 testimony-text">
                <h6>{{person.full_name}}</h6>
                <svg xmlns="http://www.w3.org/2000/svg " width="117 " height="17 " viewBox="0 0 117 17 " fill="none ">
                    <path d="M8.5 3.90594L9.3633 5.98156L9.71515 6.8275L10.6284 6.90071L12.8692 7.08036L11.162 8.5428L10.4661 9.13884L10.6787 10.03L11.2003 12.2167L9.28188 11.0449L8.5 10.5673L7.71812 11.0449L5.79968 12.2167L6.32127 10.03L6.53385 9.13884L5.83804
                8.5428L4.13079 7.08036L6.37159 6.90071L7.28486 6.8275L7.6367 5.98156L8.5 3.90594Z " fill="#F1C644 " stroke="#F1C644 " stroke-width="3 "/>
                    <path d="M33 4.11497L33.7192 5.95229L34.0593 6.82093L34.9887 6.9001L37.0582 7.07637L35.4142 8.57266L34.7692 9.15966L34.9602 10.0106L35.4328 12.1157L33.8166 11.0667L33 10.5368L32.1834 11.0667L30.5672 12.1157L31.0398 10.0106L31.2308
                9.15966L30.5858 8.57266L28.9418 7.07637L31.0113 6.9001L31.9407 6.82093L32.2808 5.95229L33 4.11497Z " fill="#F1C644 " stroke="#F1C644 " stroke-width="3 "/>
                    <path d="M58.5 3.90594L59.3633 5.98156L59.7151 6.8275L60.6284 6.90071L62.8692 7.08036L61.162 8.5428L60.4661 9.13884L60.6787 10.03L61.2003 12.2167L59.2819 11.0449L58.5 10.5673L57.7181 11.0449L55.7997 12.2167L56.3213 10.03L56.5339
                9.13884L55.838 8.5428L54.1308 7.08036L56.3716 6.90071L57.2849 6.8275L57.6367 5.98156L58.5 3.90594Z " fill="#F1C644 " stroke="#F1C644 " stroke-width="3 "/>
                    <path d="M84 4.11497L84.7192 5.95229L85.0593 6.82093L85.9887 6.9001L88.0582 7.07637L86.4142 8.57266L85.7692 9.15966L85.9602 10.0106L86.4328 12.1157L84.8166 11.0667L84 10.5368L83.1834 11.0667L81.5672 12.1157L82.0398 10.0106L82.2308
                9.15966L81.5858 8.57266L79.9418 7.07637L82.0113 6.9001L82.9407 6.82093L83.2808 5.95229L84 4.11497Z " fill="#F1C644 " stroke="#F1C644 " stroke-width="3 "/>
                    <path d="M108.5 0L110.748 5.40551L116.584 5.87336L112.138 9.68199L113.496 15.3766L108.5 12.325L103.504 15.3766L104.862 9.68199L100.416 5.87336L106.252 5.40551L108.5 0Z " fill="#D4D4D4 "/>
                  </svg>
                <p>
                    {{person.message|richtext}}
                </p>
            </div>
        </div>
    </div>
    {% endfor %} {% else %}
    </div>{% endif %}
</section>
{% endif %}

<section class="container faq" loading="lazy">
    <div class="section-title row wow animate__animated animate__slideInLeft">
        <h1>Frequently asked Question</h1>
        <!-- <p>Getting started is easy all you need to do is follow the steps below</p> -->
    </div>
    <div class="accordion accordion-flush" id="accordionFlushExample">
        {% if faqs %}{% for faq in faqs %}
        <div class="accordion-item wow animate__animated animate__slideInUp">
            <h2 class="accordion-header" id="{{faq.accordion_heading_id}}">
                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#{{faq.flush_collapse_id}}" aria-expanded="false" aria-controls="{{faq.flush_collapse_id}}">
                {{faq.question}} 
            </button>
            </h2>
            <div id="{{faq.flush_collapse_id}}" class="accordion-collapse collapse" aria-labelledby="{{faq.accordion_heading_id}}" data-bs-parent="#accordionFlushExample">
                <div class="accordion-body">{{faq.answer|richtext}}</div>
            </div>
        </div>
        {% endfor %}{% else %}
        <div></div>
        {% endif %}
    </div>
</section>
<section class="waitlist-form container faq" loading="lazy" id="join-waitlist">
    <div class="section-title section-header row">
        <h1>Join the exclusive <span>waitlist</span></h1>
    </div>
    {% include 'waitlist/waitlist_form.html' %}
</section>

{% endblock %}