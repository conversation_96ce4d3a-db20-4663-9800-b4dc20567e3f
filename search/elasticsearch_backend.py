"""
Elasticsearch backend for advanced search functionality.
This provides a scalable search solution for large datasets.
"""

from elasticsearch import Elasticsearch
from elasticsearch.exceptions import NotFoundError
from django.conf import settings
from django.core.management.base import BaseCommand
import json
import logging

logger = logging.getLogger(__name__)


class ElasticsearchBackend:
    """
    Elasticsearch backend for search operations.
    """
    
    def __init__(self):
        self.es = Elasticsearch([
            settings.ELASTICSEARCH_URL
        ]) if hasattr(settings, 'ELASTICSEARCH_URL') else None
        
        self.indices = {
            'companies': 'connectize_companies',
            'products': 'connectize_products',
            'services': 'connectize_services',
            'posts': 'connectize_posts'
        }
    
    def create_indices(self):
        """Create Elasticsearch indices with proper mappings."""
        if not self.es:
            logger.warning("Elasticsearch not configured")
            return
            
        # Company index mapping
        company_mapping = {
            "mappings": {
                "properties": {
                    "company_name": {
                        "type": "text",
                        "analyzer": "standard",
                        "fields": {
                            "keyword": {"type": "keyword"},
                            "suggest": {
                                "type": "completion",
                                "analyzer": "simple"
                            }
                        }
                    },
                    "about": {"type": "text", "analyzer": "english"},
                    "tag_line": {"type": "text", "analyzer": "english"},
                    "country": {"type": "keyword"},
                    "city": {"type": "keyword"},
                    "office_address": {"type": "text"},
                    "organization_type": {"type": "keyword"},
                    "date_created": {"type": "date"}
                }
            },
            "settings": {
                "analysis": {
                    "analyzer": {
                        "autocomplete": {
                            "tokenizer": "autocomplete",
                            "filter": ["lowercase"]
                        }
                    },
                    "tokenizer": {
                        "autocomplete": {
                            "type": "edge_ngram",
                            "min_gram": 2,
                            "max_gram": 10,
                            "token_chars": ["letter", "digit"]
                        }
                    }
                }
            }
        }
        
        # Product index mapping
        product_mapping = {
            "mappings": {
                "properties": {
                    "title": {
                        "type": "text",
                        "analyzer": "english",
                        "fields": {
                            "keyword": {"type": "keyword"},
                            "suggest": {
                                "type": "completion",
                                "analyzer": "simple"
                            }
                        }
                    },
                    "sub_title": {"type": "text", "analyzer": "english"},
                    "description": {"type": "text", "analyzer": "english"},
                    "category": {"type": "keyword"},
                    "company_name": {"type": "keyword"},
                    "featured": {"type": "boolean"},
                    "date_created": {"type": "date"}
                }
            }
        }
        
        # Create indices
        for index_name, mapping in [
            (self.indices['companies'], company_mapping),
            (self.indices['products'], product_mapping)
        ]:
            try:
                if self.es.indices.exists(index=index_name):
                    self.es.indices.delete(index=index_name)
                self.es.indices.create(index=index_name, body=mapping)
                logger.info(f"Created Elasticsearch index: {index_name}")
            except Exception as e:
                logger.error(f"Failed to create index {index_name}: {e}")
    
    def index_company(self, company):
        """Index a single company."""
        if not self.es:
            return
            
        doc = {
            'company_name': company.company_name,
            'about': company.about,
            'tag_line': company.tag_line,
            'country': company.country,
            'city': company.city,
            'office_address': company.office_address,
            'organization_type': company.organization_type.name if company.organization_type else None,
            'date_created': company.date_created
        }
        
        try:
            self.es.index(
                index=self.indices['companies'],
                id=company.id,
                body=doc
            )
        except Exception as e:
            logger.error(f"Failed to index company {company.id}: {e}")
    
    def search_companies(self, query, limit=20):
        """Search companies using Elasticsearch."""
        if not self.es:
            return []
            
        search_body = {
            "query": {
                "multi_match": {
                    "query": query,
                    "fields": [
                        "company_name^3",
                        "about^2",
                        "tag_line^2",
                        "country",
                        "city",
                        "office_address"
                    ],
                    "type": "best_fields",
                    "fuzziness": "AUTO"
                }
            },
            "highlight": {
                "fields": {
                    "company_name": {},
                    "about": {},
                    "tag_line": {}
                }
            },
            "size": limit
        }
        
        try:
            response = self.es.search(
                index=self.indices['companies'],
                body=search_body
            )
            return response['hits']['hits']
        except Exception as e:
            logger.error(f"Elasticsearch search failed: {e}")
            return []
    
    def autocomplete_suggestions(self, query, limit=10):
        """Get autocomplete suggestions."""
        if not self.es:
            return []
            
        search_body = {
            "suggest": {
                "company_suggest": {
                    "prefix": query,
                    "completion": {
                        "field": "company_name.suggest",
                        "size": limit
                    }
                }
            }
        }
        
        try:
            response = self.es.search(
                index=self.indices['companies'],
                body=search_body
            )
            suggestions = []
            for suggestion in response['suggest']['company_suggest'][0]['options']:
                suggestions.append(suggestion['text'])
            return suggestions
        except Exception as e:
            logger.error(f"Autocomplete failed: {e}")
            return []


# Management command to set up Elasticsearch
class Command(BaseCommand):
    help = 'Set up Elasticsearch indices and mappings'
    
    def handle(self, *args, **options):
        backend = ElasticsearchBackend()
        backend.create_indices()
        self.stdout.write(
            self.style.SUCCESS('Successfully set up Elasticsearch indices')
        )
