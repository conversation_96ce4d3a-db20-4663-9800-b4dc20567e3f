# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-08-19 15:33-0500\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: dashboard/dashboard.py:210
msgid "Quick links"
msgstr "Enlaces Rápidos"

#: dashboard/dashboard.py:216
msgid "Return to site"
msgstr "Volver al sitio"

#: dashboard/dashboard.py:217
msgid "Change password"
msgstr "Cambiar contraseña"

#: dashboard/dashboard.py:219
msgid "Log out"
msgstr "Cerrar sesión"

#: dashboard/dashboard.py:227 dashboard/modules.py:293
msgid "Applications"
msgstr "Aplicaciones"

#: dashboard/dashboard.py:235
msgid "Administration"
msgstr "Administración"

#: dashboard/dashboard.py:243 dashboard/modules.py:438
msgid "Recent Actions"
msgstr "Acciones recientes"

#: dashboard/dashboard.py:251
msgid "Latest Django News"
msgstr "Últimas Noticias de Django"

#: dashboard/dashboard.py:260
msgid "Support"
msgstr "Soporte"

#: dashboard/dashboard.py:263
msgid "Django documentation"
msgstr "Documentación Django"

#: dashboard/dashboard.py:268
msgid "Django \"django-users\" mailing list"
msgstr "Lista de correos Django \"django-users\""

#: dashboard/dashboard.py:273
msgid "Django irc channel"
msgstr "Canal IRC de Django"

#: dashboard/dashboard.py:288
msgid "Application models"
msgstr "Modelos de la aplicación"

#: dashboard/dashboard_modules/google_analytics.py:145
#: dashboard/dashboard_modules/yandex_metrika.py:103
msgid "Revoke access"
msgstr "Revocar acceso"

#: dashboard/dashboard_modules/google_analytics.py:150
#: dashboard/dashboard_modules/yandex_metrika.py:108
msgid "Grant access"
msgstr "Garantizar acceso"

#: dashboard/dashboard_modules/google_analytics.py:163
#: dashboard/dashboard_modules/yandex_metrika.py:118
msgid "Access"
msgstr "Acceso"

#: dashboard/dashboard_modules/google_analytics.py:164
#: dashboard/dashboard_modules/yandex_metrika.py:119
msgid "Counter"
msgstr "Contador"

#: dashboard/dashboard_modules/google_analytics.py:165
#: dashboard/dashboard_modules/yandex_metrika.py:120
msgid "Statistics period"
msgstr "Periodo de estadísticas"

#: dashboard/dashboard_modules/google_analytics.py:166
#: dashboard/dashboard_modules/yandex_metrika.py:121
msgid "Today"
msgstr "Hoy"

#: dashboard/dashboard_modules/google_analytics.py:167
#: dashboard/dashboard_modules/yandex_metrika.py:122
msgid "Last week"
msgstr "Última semana"

#: dashboard/dashboard_modules/google_analytics.py:168
#: dashboard/dashboard_modules/yandex_metrika.py:123
msgid "Last month"
msgstr "Último mes"

#: dashboard/dashboard_modules/google_analytics.py:169
#: dashboard/dashboard_modules/yandex_metrika.py:124
msgid "Last quarter"
msgstr "Último cuatrimestre"

#: dashboard/dashboard_modules/google_analytics.py:170
#: dashboard/dashboard_modules/yandex_metrika.py:125
msgid "Last year"
msgstr "Último año"

#: dashboard/dashboard_modules/google_analytics.py:180
#: dashboard/dashboard_modules/yandex_metrika.py:135
msgid "none"
msgstr "ninguno"

#: dashboard/dashboard_modules/google_analytics.py:183
#: dashboard/dashboard_modules/yandex_metrika.py:138
msgid "grant access first"
msgstr "garantizar primer acceso"

#: dashboard/dashboard_modules/google_analytics.py:183
#: dashboard/dashboard_modules/yandex_metrika.py:138
msgid "counters loading failed"
msgstr "falló la carga de contadores"

#: dashboard/dashboard_modules/google_analytics.py:188
#: dashboard/dashboard_modules/yandex_metrika.py:143
msgid "Show"
msgstr "Mostrar"

#: dashboard/dashboard_modules/google_analytics.py:189
#: dashboard/dashboard_modules/google_analytics.py:326
#: dashboard/templates/jet.dashboard/modules/google_analytics_period_visitors.html:15
msgid "users"
msgstr "usuarios"

#: dashboard/dashboard_modules/google_analytics.py:190
#: dashboard/dashboard_modules/google_analytics.py:327
#: dashboard/templates/jet.dashboard/modules/google_analytics_period_visitors.html:16
msgid "sessions"
msgstr "sesiones"

#: dashboard/dashboard_modules/google_analytics.py:191
#: dashboard/dashboard_modules/google_analytics.py:328
#: dashboard/dashboard_modules/yandex_metrika.py:146
#: dashboard/dashboard_modules/yandex_metrika.py:267
#: dashboard/templates/jet.dashboard/modules/google_analytics_period_visitors.html:17
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:17
msgid "views"
msgstr "vistas"

#: dashboard/dashboard_modules/google_analytics.py:193
#: dashboard/dashboard_modules/google_analytics.py:201
#: dashboard/dashboard_modules/yandex_metrika.py:148
#: dashboard/dashboard_modules/yandex_metrika.py:156
msgid "Group"
msgstr "Grupo"

#: dashboard/dashboard_modules/google_analytics.py:194
#: dashboard/dashboard_modules/google_analytics.py:202
#: dashboard/dashboard_modules/yandex_metrika.py:149
#: dashboard/dashboard_modules/yandex_metrika.py:157
msgid "By day"
msgstr "Por día"

#: dashboard/dashboard_modules/google_analytics.py:195
#: dashboard/dashboard_modules/google_analytics.py:203
#: dashboard/dashboard_modules/yandex_metrika.py:150
#: dashboard/dashboard_modules/yandex_metrika.py:158
msgid "By week"
msgstr "Por semana"

#: dashboard/dashboard_modules/google_analytics.py:196
#: dashboard/dashboard_modules/google_analytics.py:204
#: dashboard/dashboard_modules/yandex_metrika.py:151
#: dashboard/dashboard_modules/yandex_metrika.py:159
msgid "By month"
msgstr "Por mes"

#: dashboard/dashboard_modules/google_analytics.py:277
#, python-format
msgid ""
"Please <a href=\"%s\">attach Google account and choose Google Analytics "
"counter</a> to start using widget"
msgstr ""
"Por favor <a href=\"%s\">agregue una cuenta Google y elija un contador "
"Google Analytics</a> para comenzar a usar este artilugio."

#: dashboard/dashboard_modules/google_analytics.py:280
#, python-format
msgid ""
"Please <a href=\"%s\">select Google Analytics counter</a> to start using "
"widget"
msgstr ""
"Por favor <a href=\"%s\">seleccione un contador Google Analytics</a> para "
"comenzar a usar este artilugio"

#: dashboard/dashboard_modules/google_analytics.py:299
#: dashboard/dashboard_modules/google_analytics_views.py:42
#: dashboard/dashboard_modules/yandex_metrika.py:236
#: dashboard/dashboard_modules/yandex_metrika_views.py:37
msgid "API request failed."
msgstr "Falló la petición API."

#: dashboard/dashboard_modules/google_analytics.py:301
#: dashboard/dashboard_modules/yandex_metrika.py:238
#, python-format
msgid " Try to <a href=\"%s\">revoke and grant access</a> again"
msgstr " Intente <a href=\"%s\">revocar y garantizar el acceso</a> nuevamente"

#: dashboard/dashboard_modules/google_analytics.py:311
msgid "Google Analytics visitors totals"
msgstr "Total de visitantes Google Analytics"

#: dashboard/dashboard_modules/google_analytics.py:330
#: dashboard/dashboard_modules/google_analytics.py:388
#: dashboard/dashboard_modules/google_analytics.py:438
#: dashboard/dashboard_modules/yandex_metrika.py:269
#: dashboard/dashboard_modules/yandex_metrika.py:321
#: dashboard/dashboard_modules/yandex_metrika.py:365
msgid "Bad server response"
msgstr "Respuesta negativa del servidor"

#: dashboard/dashboard_modules/google_analytics.py:340
msgid "Google Analytics visitors chart"
msgstr "Gráfico de visitantes Google Analytics"

#: dashboard/dashboard_modules/google_analytics.py:398
msgid "Google Analytics period visitors"
msgstr "Periodo de visitantes Google Analytics"

#: dashboard/dashboard_modules/google_analytics_views.py:26
#: dashboard/dashboard_modules/google_analytics_views.py:46
#: dashboard/dashboard_modules/yandex_metrika_views.py:23
#: dashboard/dashboard_modules/yandex_metrika_views.py:45
msgid "Module not found"
msgstr "No se encontró el módulo"

#: dashboard/dashboard_modules/google_analytics_views.py:44
#: dashboard/dashboard_modules/yandex_metrika_views.py:43
msgid "Bad arguments"
msgstr "Argumentos erróneos"

#: dashboard/dashboard_modules/yandex_metrika.py:144
#: dashboard/dashboard_modules/yandex_metrika.py:265
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:15
msgid "visitors"
msgstr "visitantes"

#: dashboard/dashboard_modules/yandex_metrika.py:145
#: dashboard/dashboard_modules/yandex_metrika.py:266
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:16
msgid "visits"
msgstr "visitas"

#: dashboard/dashboard_modules/yandex_metrika.py:219
#, python-format
msgid ""
"Please <a href=\"%s\">attach Yandex account and choose Yandex Metrika "
"counter</a> to start using widget"
msgstr ""
"Por favor <a href=\"%s\">agregue una cuenta Yandex y elija un contador "
"Yandex Metrika </a> para comenzar a usar este artilugio"

#: dashboard/dashboard_modules/yandex_metrika.py:222
#, python-format
msgid ""
"Please <a href=\"%s\">select Yandex Metrika counter</a> to start using widget"
msgstr ""
"Por favor <a href=\"%s\">seleccion un contador Yandex Metrika</a> para "
"comenzar a usar este artilugio"

#: dashboard/dashboard_modules/yandex_metrika.py:250
msgid "Yandex Metrika visitors totals"
msgstr "Total de visitantes Yandex Metrika"

#: dashboard/dashboard_modules/yandex_metrika.py:279
msgid "Yandex Metrika visitors chart"
msgstr "Gráfica de visitantes Yandex Metrika"

#: dashboard/dashboard_modules/yandex_metrika.py:331
msgid "Yandex Metrika period visitors"
msgstr "Periodo de visitantes Yandex Metrika"

#: dashboard/models.py:11 dashboard/modules.py:164
msgid "Title"
msgstr "Título"

#: dashboard/models.py:12
msgid "module"
msgstr "módulo"

#: dashboard/models.py:13
msgid "application name"
msgstr "nombre de la aplicación"

#: dashboard/models.py:14
msgid "user"
msgstr "usuario"

#: dashboard/models.py:15
msgid "column"
msgstr "columna"

#: dashboard/models.py:16
msgid "order"
msgstr "orden"

#: dashboard/models.py:17
msgid "settings"
msgstr "opciones"

#: dashboard/models.py:18
msgid "children"
msgstr "hijos"

#: dashboard/models.py:19
msgid "collapsed"
msgstr "colapsada"

#: dashboard/models.py:22
msgid "user dashboard module"
msgstr "módulo de usuario en tablero"

#: dashboard/models.py:23
msgid "user dashboard modules"
msgstr "módulos de usuario en tablero"

#: dashboard/modules.py:163
msgid "URL"
msgstr "URL"

#: dashboard/modules.py:165
msgid "External link"
msgstr "Enlace externo"

#: dashboard/modules.py:169
msgid "Layout"
msgstr "Disposición"

#: dashboard/modules.py:169
msgid "Stacked"
msgstr "Apilado"

#: dashboard/modules.py:169
msgid "Inline"
msgstr "En linea"

#: dashboard/modules.py:215 dashboard/modules.py:239
msgid "Links"
msgstr "Enlaces"

#: dashboard/modules.py:238
msgid "Link"
msgstr "Enlace"

#: dashboard/modules.py:365
msgid "Models"
msgstr "Modelos"

#: dashboard/modules.py:408 dashboard/modules.py:515
msgid "Items limit"
msgstr "Límite de elementos"

#: dashboard/modules.py:516
msgid "Feed URL"
msgstr "URL de Alimentador"

#: dashboard/modules.py:547
msgid "RSS Feed"
msgstr "Alimentador RSS"

#: dashboard/modules.py:593
msgid "You must install the FeedParser python module"
msgstr "Debe instalar el módulo python FeedParser"

#: dashboard/modules.py:598
msgid "You must provide a valid feed URL"
msgstr "Debe proveer un URL de alimentador válido"

#: dashboard/templates/jet.dashboard/dashboard.html:17
msgid "Delete widget"
msgstr "Eliminar Artilugio"

#: dashboard/templates/jet.dashboard/dashboard.html:18
msgid "Are you sure want to delete this widget?"
msgstr "¿Está seguro de que desea eliminar este artilugio?"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:12
msgid "widgets"
msgstr "artilugios"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:13
msgid "available"
msgstr "habilitado"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:18
msgid "initials"
msgstr "iniciales"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:23
#: dashboard/templates/jet.dashboard/modules/app_list.html:18
#: dashboard/templates/jet.dashboard/modules/model_list.html:8
msgid "Add"
msgstr "Añadir"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:26
#: dashboard/templates/jet.dashboard/dashboard_tools.html:32
msgid "Reset widgets"
msgstr "Reiniciar artilugios"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:33
msgid "Are you sure want to reset widgets?"
msgstr "Está seguro de que quiere reiniciar este artilugio?"

#: dashboard/templates/jet.dashboard/module.html:19
#: dashboard/templates/jet.dashboard/modules/app_list.html:24
#: dashboard/templates/jet.dashboard/modules/model_list.html:14
#: dashboard/views.py:89
msgid "Change"
msgstr "Cambiar"

#: dashboard/templates/jet.dashboard/module.html:22
#: dashboard/templates/jet.dashboard/update_module.html:62
#: dashboard/templates/jet.dashboard/update_module.html:64
msgid "Delete"
msgstr "Eliminar"

#: dashboard/templates/jet.dashboard/modules/app_list.html:7
#: dashboard/templates/jet.dashboard/modules/app_list.html:10
#, python-format
msgid "Models in the %(name)s application"
msgstr "Modelos en la aplicación %(name)s"

#: dashboard/templates/jet.dashboard/modules/feed.html:13
#: dashboard/templates/jet.dashboard/modules/google_analytics_period_visitors.html:34
#: dashboard/templates/jet.dashboard/modules/google_analytics_visitors_chart.html:30
#: dashboard/templates/jet.dashboard/modules/google_analytics_visitors_totals.html:23
#: dashboard/templates/jet.dashboard/modules/link_list.html:26
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:34
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_visitors_chart.html:30
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_visitors_totals.html:23
msgid "Nothing to show"
msgstr "Nada para mostrar"

#: dashboard/templates/jet.dashboard/modules/google_analytics_period_visitors.html:14
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:14
msgid "Date"
msgstr "Fecha"

#: dashboard/templates/jet.dashboard/modules/recent_actions.html:6
msgid "None available"
msgstr "Ninguno habilitado"

#: dashboard/templates/jet.dashboard/modules/recent_actions.html:30
msgid "Unknown content"
msgstr "Contenido desconocido"

#: dashboard/templates/jet.dashboard/update_module.html:14
msgid "Home"
msgstr "Inicio"

#: dashboard/templates/jet.dashboard/update_module.html:32
msgid "Please correct the errors below."
msgstr "Por favor corrija los presentes debajo."

#: dashboard/templates/jet.dashboard/update_module.html:76
#, python-format
msgid "Add another %(verbose_name)s"
msgstr "Añadir otro %(verbose_name)s"

#: dashboard/templates/jet.dashboard/update_module.html:88
msgid "Save"
msgstr "Guardar"

#: dashboard/views.py:18
msgid "Widget was successfully updated"
msgstr "El artilugio fue actualizado exitosamente"

#: dashboard/views.py:93 dashboard/views.py:94
msgid "Items"
msgstr "Elementos"

#: dashboard/views.py:160
msgid "Widget has been successfully added"
msgstr "El artilugio ha sido exitosamente añadido"
