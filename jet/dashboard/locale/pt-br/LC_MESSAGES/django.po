# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-09-21 10:34-0300\n"
"PO-Revision-Date: 2016-09-21 10:34-0300\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 "
"&& (n%100<10 || n%100>=20) ? 1 : 2);\n"
"Language-Team: \n"
"X-Generator: Poedit 1.8.9\n"

#: dashboard/modules.py:138 templates/admin/base.html:281
msgid "URL"
msgstr "URL"

#: dashboard/dashboard.py:207
msgid "Quick links"
msgstr "Atalhos"

#: dashboard/dashboard.py:213
msgid "Return to site"
msgstr "Voltar ao site"

#: dashboard/dashboard.py:224 dashboard/modules.py:268
msgid "Applications"
msgstr "Aplicativos"

#: dashboard/dashboard.py:232
msgid "Administration"
msgstr "Administração"

#: dashboard/dashboard.py:240 dashboard/modules.py:413
msgid "Recent Actions"
msgstr "Ações Recentes"

#: dashboard/dashboard.py:248
msgid "Latest Django News"
msgstr "Últimas notícias do Django"

#: dashboard/dashboard.py:257
msgid "Support"
msgstr "Suporte"

#: dashboard/dashboard.py:260
msgid "Django documentation"
msgstr "Documentação do Django"

#: dashboard/dashboard.py:265
msgid "Django \"django-users\" mailing list"
msgstr "Lista de discussão de Django \"django-usuários\""

#: dashboard/dashboard.py:270
msgid "Django irc channel"
msgstr "Canal irc do Django"

#: dashboard/dashboard.py:285
msgid "Application models"
msgstr "Aplicativos"

#: dashboard/models.py:11 dashboard/modules.py:139
msgid "Title"
msgstr "Título"

#: dashboard/modules.py:140
msgid "External link"
msgstr "Link Externo"

#: dashboard/modules.py:144
msgid "Layout"
msgstr "Layout"

#: dashboard/modules.py:144
msgid "Stacked"
msgstr "Empilhado"

#: dashboard/modules.py:144
msgid "Inline"
msgstr "Em linha"

#: dashboard/modules.py:190 dashboard/modules.py:214
msgid "Links"
msgstr "Links"

#: dashboard/modules.py:213
msgid "Link"
msgstr "Link"

#: dashboard/modules.py:340
msgid "Models"
msgstr "Models"

#: dashboard/modules.py:383 dashboard/modules.py:490
msgid "Items limit"
msgstr "Limite de itens"

#: dashboard/modules.py:491
msgid "Feed URL"
msgstr "URL de Feed"

#: dashboard/modules.py:522
msgid "RSS Feed"
msgstr "Feed RSS"

#: dashboard/modules.py:568
msgid "You must install the FeedParser python module"
msgstr "Você deve instalar o módulo FeedParser"

#: dashboard/modules.py:573
msgid "You must provide a valid feed URL"
msgstr "Você deve informar uma URL de feed válida"

#: dashboard/views.py:17
msgid "Widget was successfully updated"
msgstr "Widget atualizado com sucesso"

#: dashboard/views.py:89 dashboard/views.py:90
msgid "Items"
msgstr "Itens"

#: dashboard/views.py:152
msgid "Widget has been successfully added"
msgstr "Widget adicionado com sucesso"

#: dashboard/dashboard_modules/google_analytics.py:145
#: dashboard/dashboard_modules/yandex_metrika.py:102
msgid "Revoke access"
msgstr "Revogar acesso"

#: dashboard/dashboard_modules/google_analytics.py:150
#: dashboard/dashboard_modules/yandex_metrika.py:107
msgid "Grant access"
msgstr "Permitir acesso"

#: dashboard/dashboard_modules/google_analytics.py:163
#: dashboard/dashboard_modules/yandex_metrika.py:117
msgid "Access"
msgstr "Acesso"

#: dashboard/dashboard_modules/google_analytics.py:164
#: dashboard/dashboard_modules/yandex_metrika.py:118
msgid "Counter"
msgstr "Contador"

#: dashboard/dashboard_modules/google_analytics.py:165
#: dashboard/dashboard_modules/yandex_metrika.py:119
msgid "Statistics period"
msgstr "Período de Estatísticas"

#: dashboard/dashboard_modules/google_analytics.py:166
#: dashboard/dashboard_modules/yandex_metrika.py:120
msgid "Today"
msgstr "Hoje"

#: dashboard/dashboard_modules/google_analytics.py:167
#: dashboard/dashboard_modules/yandex_metrika.py:121
msgid "Last week"
msgstr "Última semana"

#: dashboard/dashboard_modules/google_analytics.py:168
#: dashboard/dashboard_modules/yandex_metrika.py:122
msgid "Last month"
msgstr "Último mês"

#: dashboard/dashboard_modules/google_analytics.py:169
#: dashboard/dashboard_modules/yandex_metrika.py:123
msgid "Last quarter"
msgstr "Último trimestre"

#: dashboard/dashboard_modules/google_analytics.py:170
#: dashboard/dashboard_modules/yandex_metrika.py:124
msgid "Last year"
msgstr "Último ano"

#: dashboard/dashboard_modules/google_analytics.py:180
#: dashboard/dashboard_modules/yandex_metrika.py:134
msgid "none"
msgstr "nenhum"

#: dashboard/dashboard_modules/google_analytics.py:183
#: dashboard/dashboard_modules/yandex_metrika.py:137
msgid "grant access first"
msgstr "conceder acesso antes"

#: dashboard/dashboard_modules/google_analytics.py:183
#: dashboard/dashboard_modules/yandex_metrika.py:137
msgid "counters loading failed"
msgstr "carregamento do contador falhou"

#: dashboard/dashboard_modules/google_analytics.py:188
#: dashboard/dashboard_modules/yandex_metrika.py:142
msgid "Show"
msgstr "Exibir"

#: dashboard/dashboard_modules/google_analytics.py:193
#: dashboard/dashboard_modules/google_analytics.py:201
#: dashboard/dashboard_modules/yandex_metrika.py:147
#: dashboard/dashboard_modules/yandex_metrika.py:155
msgid "Group"
msgstr "Grupo"

#: dashboard/dashboard_modules/google_analytics.py:194
#: dashboard/dashboard_modules/google_analytics.py:202
#: dashboard/dashboard_modules/yandex_metrika.py:148
#: dashboard/dashboard_modules/yandex_metrika.py:156
msgid "By day"
msgstr "Por dia"

#: dashboard/dashboard_modules/google_analytics.py:195
#: dashboard/dashboard_modules/google_analytics.py:203
#: dashboard/dashboard_modules/yandex_metrika.py:149
#: dashboard/dashboard_modules/yandex_metrika.py:157
msgid "By week"
msgstr "Por semana"

#: dashboard/dashboard_modules/google_analytics.py:196
#: dashboard/dashboard_modules/google_analytics.py:204
#: dashboard/dashboard_modules/yandex_metrika.py:150
#: dashboard/dashboard_modules/yandex_metrika.py:158
msgid "By month"
msgstr "Por mês"

#: dashboard/dashboard_modules/google_analytics.py:277
#, python-format
msgid ""
"Please <a href=\"%s\">attach Google account and choose Google Analytics counter</"
"a> to start using widget"
msgstr ""
"Por favor <a href=\"%s\"> anexe uma conta do Google e selecione contadorGoogle "
"Analytics</a> para usar o widget "

#: dashboard/dashboard_modules/google_analytics.py:280
#, python-format
msgid ""
"Please <a href=\"%s\">select Google Analytics counter</a> to start using widget"
msgstr ""
"Por favor <a href=\"%s\">selecione contador google Analytics</a> para usar o "
"widget"

#: dashboard/dashboard_modules/google_analytics.py:299
#: dashboard/dashboard_modules/google_analytics_views.py:42
#: dashboard/dashboard_modules/yandex_metrika.py:236
#: dashboard/dashboard_modules/yandex_metrika_views.py:37
msgid "API request failed."
msgstr "Requisição da API falhou"

#: dashboard/dashboard_modules/google_analytics.py:301
#: dashboard/dashboard_modules/yandex_metrika.py:238
#, python-format
msgid " Try to <a href=\"%s\">revoke and grant access</a> again"
msgstr "Tente <a href=\"%s\">revogar e conceder acesso</a> novamente"

#: dashboard/dashboard_modules/google_analytics.py:311
msgid "Google Analytics visitors totals"
msgstr "Total de visitantes pelo Google Analytics"

#: dashboard/dashboard_modules/google_analytics.py:189
#: dashboard/templates/jet.dashboard/modules/google_analytics_period_visitors.html:15
#: dashboard/dashboard_modules/google_analytics.py:326
msgid "users"
msgstr "usuários"

#: dashboard/dashboard_modules/google_analytics.py:190
#: dashboard/templates/jet.dashboard/modules/google_analytics_period_visitors.html:16
#: dashboard/dashboard_modules/google_analytics.py:327
msgid "sessions"
msgstr "sessões"

#: dashboard/dashboard_modules/google_analytics.py:191
#: dashboard/dashboard_modules/yandex_metrika.py:146
#: dashboard/templates/jet.dashboard/modules/google_analytics_period_visitors.html:17
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:17
#: dashboard/dashboard_modules/google_analytics.py:328
#: dashboard/dashboard_modules/yandex_metrika.py:267
msgid "views"
msgstr "views"

#: dashboard/dashboard_modules/google_analytics.py:330
#: dashboard/dashboard_modules/google_analytics.py:388
#: dashboard/dashboard_modules/google_analytics.py:438
#: dashboard/dashboard_modules/yandex_metrika.py:269
#: dashboard/dashboard_modules/yandex_metrika.py:321
#: dashboard/dashboard_modules/yandex_metrika.py:365
msgid "Bad server response"
msgstr "Resposta ruim do servidor"

#: dashboard/dashboard_modules/google_analytics.py:340
msgid "Google Analytics visitors chart"
msgstr "Gráfico de visitantes Google Analytics"

#: dashboard/dashboard_modules/google_analytics.py:398
msgid "Google Analytics period visitors"
msgstr "Período de visitas Google Analytics"

#: dashboard/dashboard_modules/google_analytics_views.py:26
#: dashboard/dashboard_modules/google_analytics_views.py:46
#: dashboard/dashboard_modules/yandex_metrika_views.py:23
#: dashboard/dashboard_modules/yandex_metrika_views.py:45
msgid "Module not found"
msgstr "Módulo não encontrado"

#: dashboard/dashboard_modules/google_analytics_views.py:44
#: dashboard/dashboard_modules/yandex_metrika_views.py:43
msgid "Bad arguments"
msgstr "Argumentos ruins"

#: dashboard/dashboard_modules/yandex_metrika.py:219
#, python-format
msgid ""
"Please <a href=\"%s\">attach Yandex account and choose Yandex Metrika counter</"
"a> to start using widget"
msgstr ""
"Por favor <a href=\"%s\">anexe uma conta Yandex e selecione contador Yandex "
"Metrika</a> para usar o widget"

#: dashboard/dashboard_modules/yandex_metrika.py:222
#, python-format
msgid ""
"Please <a href=\"%s\">select Yandex Metrika counter</a> to start using widget"
msgstr ""
"Por favor <a href=\"%s\">selecione contador Yandex Metrika</a> para usar o widget"

#: dashboard/dashboard_modules/yandex_metrika.py:250
msgid "Yandex Metrika visitors totals"
msgstr "Totais de visitantes Yandex Metrika"

#: dashboard/dashboard_modules/yandex_metrika.py:144
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:15
#: dashboard/dashboard_modules/yandex_metrika.py:265
msgid "visitors"
msgstr "visitantes"

#: dashboard/dashboard_modules/yandex_metrika.py:145
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:16
#: dashboard/dashboard_modules/yandex_metrika.py:266
msgid "visits"
msgstr "visitas"

#: dashboard/dashboard_modules/yandex_metrika.py:279
msgid "Yandex Metrika visitors chart"
msgstr "Gráfico de visitantes Yandex Metrika"

#: dashboard/dashboard_modules/yandex_metrika.py:331
msgid "Yandex Metrika period visitors"
msgstr "Visitantes no período Yandex Metrika"

#: dashboard/templates/jet.dashboard/dashboard.html:17
msgid "Delete widget"
msgstr "Deletar widget"

#: dashboard/templates/jet.dashboard/dashboard.html:18
msgid "Are you sure want to delete this widget?"
msgstr "Tem certeza que deseja deletar este widget?"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:7
msgid "widgets"
msgstr "widgets"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:8
msgid "available"
msgstr "disponível"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:13
msgid "initials"
msgstr "iniciais"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:21
#: dashboard/templates/jet.dashboard/dashboard_tools.html:27
msgid "Reset widgets"
msgstr "Reiniciar widgets"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:28
msgid "Are you sure want to reset widgets?"
msgstr "Tem certeza que deseja reiniciar os widgets?"

#: dashboard/templates/jet.dashboard/modules/feed.html:13
#: dashboard/templates/jet.dashboard/modules/google_analytics_period_visitors.html:34
#: dashboard/templates/jet.dashboard/modules/google_analytics_visitors_chart.html:30
#: dashboard/templates/jet.dashboard/modules/google_analytics_visitors_totals.html:23
#: dashboard/templates/jet.dashboard/modules/link_list.html:26
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:34
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_visitors_chart.html:30
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_visitors_totals.html:23
msgid "Nothing to show"
msgstr "Nada para exibir"
