# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-05-25 12:27+0430\n"
"PO-Revision-Date: 2017-05-24 23:51+0430\n"
"Last-Translator: Py<PERSON>berg <<EMAIL>>\n"
"Language-Team: \n"
"Language: fa\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Poedit 1.8.9\n"

#: dashboard/dashboard.py:215
msgid "Quick links"
msgstr "لینک های سریع"

#: dashboard/dashboard.py:221
msgid "Return to site"
msgstr "بازگش به سایت"

#: dashboard/dashboard.py:222
msgid "Change password"
msgstr "تغییر رمزعبور"

#: dashboard/dashboard.py:224
msgid "Log out"
msgstr "خروج"

#: dashboard/dashboard.py:232 dashboard/modules.py:299
msgid "Applications"
msgstr "اپلیکیشن ها"

#: dashboard/dashboard.py:240
msgid "Administration"
msgstr "راهبری"

#: dashboard/dashboard.py:248 dashboard/modules.py:446
msgid "Recent Actions"
msgstr "کنش‌های اخیر"

#: dashboard/dashboard.py:256
msgid "Latest Django News"
msgstr "آخرین خبرهای جنگو"

#: dashboard/dashboard.py:265
msgid "Support"
msgstr "پشتیبانی"

#: dashboard/dashboard.py:268
msgid "Django documentation"
msgstr "مستندات جنگو"

#: dashboard/dashboard.py:273
msgid "Django \"django-users\" mailing list"
msgstr "فهرست ایمیلی جنگو (django-users)"

#: dashboard/dashboard.py:278
msgid "Django irc channel"
msgstr "کانال‌های irc جنگو"

#: dashboard/dashboard.py:293
msgid "Application models"
msgstr "مدل‌های اپلیکیشن"

#: dashboard/dashboard_modules/google_analytics.py:149
#: dashboard/dashboard_modules/yandex_metrika.py:107
msgid "Revoke access"
msgstr "گرفتن دسترسی"

#: dashboard/dashboard_modules/google_analytics.py:154
#: dashboard/dashboard_modules/yandex_metrika.py:112
msgid "Grant access"
msgstr "دادن دسترسی"

#: dashboard/dashboard_modules/google_analytics.py:167
#: dashboard/dashboard_modules/yandex_metrika.py:122
msgid "Access"
msgstr "دسترسی"

#: dashboard/dashboard_modules/google_analytics.py:168
#: dashboard/dashboard_modules/yandex_metrika.py:123
msgid "Counter"
msgstr "شمارشگر"

#: dashboard/dashboard_modules/google_analytics.py:169
#: dashboard/dashboard_modules/yandex_metrika.py:124
msgid "Statistics period"
msgstr "دوره آماری"

#: dashboard/dashboard_modules/google_analytics.py:170
#: dashboard/dashboard_modules/yandex_metrika.py:125
msgid "Today"
msgstr "امروز"

#: dashboard/dashboard_modules/google_analytics.py:171
#: dashboard/dashboard_modules/yandex_metrika.py:126
msgid "Last week"
msgstr "هفته پیش"

#: dashboard/dashboard_modules/google_analytics.py:172
#: dashboard/dashboard_modules/yandex_metrika.py:127
msgid "Last month"
msgstr "ماه پیش"

#: dashboard/dashboard_modules/google_analytics.py:173
#: dashboard/dashboard_modules/yandex_metrika.py:128
msgid "Last quarter"
msgstr "سه ماه پیش"

#: dashboard/dashboard_modules/google_analytics.py:174
#: dashboard/dashboard_modules/yandex_metrika.py:129
msgid "Last year"
msgstr "سال پیش"

#: dashboard/dashboard_modules/google_analytics.py:184
#: dashboard/dashboard_modules/yandex_metrika.py:139
msgid "none"
msgstr "هیچکدام"

#: dashboard/dashboard_modules/google_analytics.py:187
#: dashboard/dashboard_modules/yandex_metrika.py:142
msgid "grant access first"
msgstr "ابتدا دسترسی بدهید"

#: dashboard/dashboard_modules/google_analytics.py:187
#: dashboard/dashboard_modules/yandex_metrika.py:142
msgid "counters loading failed"
msgstr "بارگذاری شمارشگر ناموفق بود"

#: dashboard/dashboard_modules/google_analytics.py:192
#: dashboard/dashboard_modules/yandex_metrika.py:147
msgid "Show"
msgstr "نمایش"

#: dashboard/dashboard_modules/google_analytics.py:193
#: dashboard/dashboard_modules/google_analytics.py:330
#: dashboard/templates/jet.dashboard/modules/google_analytics_period_visitors.html:15
msgid "users"
msgstr "کاربران"

#: dashboard/dashboard_modules/google_analytics.py:194
#: dashboard/dashboard_modules/google_analytics.py:331
#: dashboard/templates/jet.dashboard/modules/google_analytics_period_visitors.html:16
msgid "sessions"
msgstr "نشست‌ها"

#: dashboard/dashboard_modules/google_analytics.py:195
#: dashboard/dashboard_modules/google_analytics.py:332
#: dashboard/dashboard_modules/yandex_metrika.py:150
#: dashboard/dashboard_modules/yandex_metrika.py:271
#: dashboard/templates/jet.dashboard/modules/google_analytics_period_visitors.html:17
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:17
msgid "views"
msgstr "نماها"

#: dashboard/dashboard_modules/google_analytics.py:197
#: dashboard/dashboard_modules/google_analytics.py:205
#: dashboard/dashboard_modules/yandex_metrika.py:152
#: dashboard/dashboard_modules/yandex_metrika.py:160
msgid "Group"
msgstr "گروه"

#: dashboard/dashboard_modules/google_analytics.py:198
#: dashboard/dashboard_modules/google_analytics.py:206
#: dashboard/dashboard_modules/yandex_metrika.py:153
#: dashboard/dashboard_modules/yandex_metrika.py:161
msgid "By day"
msgstr "روزانه"

#: dashboard/dashboard_modules/google_analytics.py:199
#: dashboard/dashboard_modules/google_analytics.py:207
#: dashboard/dashboard_modules/yandex_metrika.py:154
#: dashboard/dashboard_modules/yandex_metrika.py:162
msgid "By week"
msgstr "هفتگی"

#: dashboard/dashboard_modules/google_analytics.py:200
#: dashboard/dashboard_modules/google_analytics.py:208
#: dashboard/dashboard_modules/yandex_metrika.py:155
#: dashboard/dashboard_modules/yandex_metrika.py:163
msgid "By month"
msgstr "ماهانه"

#: dashboard/dashboard_modules/google_analytics.py:281
#, python-format
msgid ""
"Please <a href=\"%s\">attach Google account and choose Google Analytics "
"counter</a> to start using widget"
msgstr ""
"لطفا برای شروع به کار این ویجت <a href=\"%s\">حساب گوگل را متصل و Google "
"Analytics را انتخاب</a> نمایید"

#: dashboard/dashboard_modules/google_analytics.py:284
#, python-format
msgid ""
"Please <a href=\"%s\">select Google Analytics counter</a> to start using "
"widget"
msgstr ""
"لطفا برای شروع به کار این ویجت <a href=\"%s\">شمارنده Google Analytics را "
"انتخاب</a> نمایید"

#: dashboard/dashboard_modules/google_analytics.py:303
#: dashboard/dashboard_modules/google_analytics_views.py:46
#: dashboard/dashboard_modules/yandex_metrika.py:240
#: dashboard/dashboard_modules/yandex_metrika_views.py:41
msgid "API request failed."
msgstr "درخواست API ناموفق بود."

#: dashboard/dashboard_modules/google_analytics.py:305
#: dashboard/dashboard_modules/yandex_metrika.py:242
#, python-format
msgid " Try to <a href=\"%s\">revoke and grant access</a> again"
msgstr " سعی کنید تا مجددا <a href=\"%s\">دسترسی را گرفته یا بدهید</a> again"

#: dashboard/dashboard_modules/google_analytics.py:315
msgid "Google Analytics visitors totals"
msgstr "تعداد کل بازدیدکننده های Google Analytics"

#: dashboard/dashboard_modules/google_analytics.py:334
#: dashboard/dashboard_modules/google_analytics.py:392
#: dashboard/dashboard_modules/google_analytics.py:442
#: dashboard/dashboard_modules/yandex_metrika.py:273
#: dashboard/dashboard_modules/yandex_metrika.py:325
#: dashboard/dashboard_modules/yandex_metrika.py:369
msgid "Bad server response"
msgstr "پاسخ نامناسب از سمت سرور"

#: dashboard/dashboard_modules/google_analytics.py:344
msgid "Google Analytics visitors chart"
msgstr "نمودار بازدیدکنندگان Google Analytics"

#: dashboard/dashboard_modules/google_analytics.py:402
msgid "Google Analytics period visitors"
msgstr "بازدیدکنندگان دوره ای Google Analytics"

#: dashboard/dashboard_modules/google_analytics_views.py:30
#: dashboard/dashboard_modules/google_analytics_views.py:50
#: dashboard/dashboard_modules/yandex_metrika_views.py:27
#: dashboard/dashboard_modules/yandex_metrika_views.py:49
msgid "Module not found"
msgstr "ماژول پیدا نشد"

#: dashboard/dashboard_modules/google_analytics_views.py:48
#: dashboard/dashboard_modules/yandex_metrika_views.py:47
msgid "Bad arguments"
msgstr "پارامترهای نامناسب"

#: dashboard/dashboard_modules/yandex_metrika.py:148
#: dashboard/dashboard_modules/yandex_metrika.py:269
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:15
msgid "visitors"
msgstr "بازدیدکنندگان"

#: dashboard/dashboard_modules/yandex_metrika.py:149
#: dashboard/dashboard_modules/yandex_metrika.py:270
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:16
msgid "visits"
msgstr "بازدیدها"

#: dashboard/dashboard_modules/yandex_metrika.py:223
#, python-format
msgid ""
"Please <a href=\"%s\">attach Yandex account and choose Yandex Metrika "
"counter</a> to start using widget"
msgstr ""
"لطفا Please <a href=\"%s\">حساب Yandex را متصل و شمارشگر Yandex Metrika</a> "
"را انتخاب نمایید"

#: dashboard/dashboard_modules/yandex_metrika.py:226
#, python-format
msgid ""
"Please <a href=\"%s\">select Yandex Metrika counter</a> to start using widget"
msgstr ""
"لطفا <a href=\"%s\">شمارشگر Yandex Metrika</a> را برای شروع انتخاب نمایید"

#: dashboard/dashboard_modules/yandex_metrika.py:254
msgid "Yandex Metrika visitors totals"
msgstr "کل بازدیدهای Yandex Metrika"

#: dashboard/dashboard_modules/yandex_metrika.py:283
msgid "Yandex Metrika visitors chart"
msgstr "نمودار بازدیدهای Yandex Metrika"

#: dashboard/dashboard_modules/yandex_metrika.py:335
msgid "Yandex Metrika period visitors"
msgstr "بازدیدکنندگان دوره ای Yandex Metrika"

#: dashboard/models.py:11 dashboard/modules.py:164
msgid "Title"
msgstr "عنوان"

#: dashboard/models.py:12
msgid "module"
msgstr "ماژول"

#: dashboard/models.py:13
msgid "application name"
msgstr "نام اپلیکیشن"

#: dashboard/models.py:14
msgid "user"
msgstr "کاربر"

#: dashboard/models.py:15
msgid "column"
msgstr "ستون"

#: dashboard/models.py:16
msgid "order"
msgstr "ترتیب"

#: dashboard/models.py:17
msgid "settings"
msgstr "تنظیمات"

#: dashboard/models.py:18
msgid "children"
msgstr "زیرگروه‌ها"

#: dashboard/models.py:19
msgid "collapsed"
msgstr "بازشده"

#: dashboard/models.py:22
msgid "user dashboard module"
msgstr "ماژول داشبورد کاربر"

#: dashboard/models.py:23
msgid "user dashboard modules"
msgstr "ماژول های داشبورد کاربر"

#: dashboard/modules.py:163
msgid "URL"
msgstr "نشانی‌ اینترنتی"

#: dashboard/modules.py:165
msgid "External link"
msgstr "لینک های خارجی"

#: dashboard/modules.py:169
msgid "Layout"
msgstr "چینش"

#: dashboard/modules.py:169
msgid "Stacked"
msgstr "پشته شدن"

#: dashboard/modules.py:169
msgid "Inline"
msgstr "درون خطی"

#: dashboard/modules.py:215 dashboard/modules.py:239
msgid "Links"
msgstr "لینک‌ها"

#: dashboard/modules.py:238
msgid "Link"
msgstr "لینک"

#: dashboard/modules.py:372
msgid "Models"
msgstr "مدل‌ها"

#: dashboard/modules.py:416 dashboard/modules.py:523
msgid "Items limit"
msgstr "محدوده آیتم‌ها"

#: dashboard/modules.py:524
msgid "Feed URL"
msgstr "نشانی فید"

#: dashboard/modules.py:555
msgid "RSS Feed"
msgstr "فید RSS"

#: dashboard/modules.py:601
msgid "You must install the FeedParser python module"
msgstr "ابتدا باید ماژول پایتونی FeedParser را نصب کنید"

#: dashboard/modules.py:606
msgid "You must provide a valid feed URL"
msgstr "ابتدا باید نشانی صحیح فید را وارد کنید"

#: dashboard/templates/jet.dashboard/dashboard.html:17
msgid "Delete widget"
msgstr "حذف ویجت"

#: dashboard/templates/jet.dashboard/dashboard.html:18
msgid "Are you sure want to delete this widget?"
msgstr "آیا میخواهید این ویجت حذف شود؟"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:12
msgid "widgets"
msgstr "ویجت‌ها"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:13
msgid "available"
msgstr "موجود"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:18
msgid "initials"
msgstr "مقادیر اولیه"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:23
#: dashboard/templates/jet.dashboard/modules/app_list.html:18
#: dashboard/templates/jet.dashboard/modules/model_list.html:8
msgid "Add"
msgstr "افزودن"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:26
#: dashboard/templates/jet.dashboard/dashboard_tools.html:32
msgid "Reset widgets"
msgstr "بازنشانی ویجت‌ها"

#: dashboard/templates/jet.dashboard/dashboard_tools.html:33
msgid "Are you sure want to reset widgets?"
msgstr "آیا میخواهید ویجت ها بازنشانی شوند؟"

#: dashboard/templates/jet.dashboard/module.html:9
#: dashboard/templates/jet.dashboard/modules/app_list.html:24
#: dashboard/templates/jet.dashboard/modules/model_list.html:14
#: dashboard/views.py:94
msgid "Change"
msgstr "تغییر"

#: dashboard/templates/jet.dashboard/module.html:12
#: dashboard/templates/jet.dashboard/update_module.html:55
#: dashboard/templates/jet.dashboard/update_module.html:57
msgid "Delete"
msgstr "حذف"

#: dashboard/templates/jet.dashboard/modules/app_list.html:7
#: dashboard/templates/jet.dashboard/modules/app_list.html:10
#, python-format
msgid "Models in the %(name)s application"
msgstr "مدل‌ها در اپلیکیشن %(name)s "

#: dashboard/templates/jet.dashboard/modules/feed.html:13
#: dashboard/templates/jet.dashboard/modules/google_analytics_period_visitors.html:34
#: dashboard/templates/jet.dashboard/modules/google_analytics_visitors_chart.html:30
#: dashboard/templates/jet.dashboard/modules/google_analytics_visitors_totals.html:23
#: dashboard/templates/jet.dashboard/modules/link_list.html:26
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:34
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_visitors_chart.html:30
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_visitors_totals.html:23
msgid "Nothing to show"
msgstr "چیزی برای نمایش وجود ندارد"

#: dashboard/templates/jet.dashboard/modules/google_analytics_period_visitors.html:14
#: dashboard/templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:14
msgid "Date"
msgstr "تاریخ"

#: dashboard/templates/jet.dashboard/modules/recent_actions.html:6
msgid "None available"
msgstr "هیچکدام آماده نیست"

#: dashboard/templates/jet.dashboard/modules/recent_actions.html:30
msgid "Unknown content"
msgstr "محتوای ناشناخته"

#: dashboard/templates/jet.dashboard/update_module.html:7
msgid "Home"
msgstr "خانه"

#: dashboard/templates/jet.dashboard/update_module.html:25
msgid "Please correct the errors below."
msgstr "لطفا خطاهای زیر را برطرف نمایید."

#: dashboard/templates/jet.dashboard/update_module.html:69
#, python-format
msgid "Add another %(verbose_name)s"
msgstr "افزودن یک %(verbose_name)s دیگر"

#: dashboard/templates/jet.dashboard/update_module.html:81
msgid "Save"
msgstr "ذخیره"

#: dashboard/views.py:23
msgid "Widget was successfully updated"
msgstr "ویجت با موفقیت بروزرسانی شد"

#: dashboard/views.py:98 dashboard/views.py:99
msgid "Items"
msgstr "آیتم‌ها"

#: dashboard/views.py:165
msgid "Widget has been successfully added"
msgstr "ویجت با موفقیت افزوده شد"
