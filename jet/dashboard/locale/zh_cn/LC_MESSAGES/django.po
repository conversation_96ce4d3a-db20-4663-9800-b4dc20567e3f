# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-20 10:30+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: dashboard.py:210
msgid "Quick links"
msgstr "快捷链接"

#: dashboard.py:216
msgid "Return to site"
msgstr "返回站点"

#: dashboard.py:217
msgid "Change password"
msgstr "修改密码"

#: dashboard.py:219
msgid "Log out"
msgstr "退出"

#: dashboard.py:227 modules.py:299
msgid "Applications"
msgstr "应用"

#: dashboard.py:235
msgid "Administration"
msgstr "管理"

#: dashboard.py:243 modules.py:446
msgid "Recent Actions"
msgstr "最近操作"

#: dashboard.py:251
msgid "Latest Django News"
msgstr "Django动态"

#: dashboard.py:260
msgid "Support"
msgstr "支持"

#: dashboard.py:263
msgid "Django documentation"
msgstr "Django文档"

#: dashboard.py:268
msgid "Django \"django-users\" mailing list"
msgstr "Django \"django-users\" 邮件列表"

#: dashboard.py:273
msgid "Django irc channel"
msgstr "Django IRC频道"

#: dashboard.py:288
msgid "Application models"
msgstr "应用Models"

#: dashboard_modules/google_analytics.py:145
#: dashboard_modules/yandex_metrika.py:103
msgid "Revoke access"
msgstr "收回访问权"

#: dashboard_modules/google_analytics.py:150
#: dashboard_modules/yandex_metrika.py:108
msgid "Grant access"
msgstr "分配访问权"

#: dashboard_modules/google_analytics.py:163
#: dashboard_modules/yandex_metrika.py:118
msgid "Access"
msgstr "访问"

#: dashboard_modules/google_analytics.py:164
#: dashboard_modules/yandex_metrika.py:119
msgid "Counter"
msgstr "统计"

#: dashboard_modules/google_analytics.py:165
#: dashboard_modules/yandex_metrika.py:120
msgid "Statistics period"
msgstr "统计周期"

#: dashboard_modules/google_analytics.py:166
#: dashboard_modules/yandex_metrika.py:121
msgid "Today"
msgstr "今日"

#: dashboard_modules/google_analytics.py:167
#: dashboard_modules/yandex_metrika.py:122
msgid "Last week"
msgstr "上周"

#: dashboard_modules/google_analytics.py:168
#: dashboard_modules/yandex_metrika.py:123
msgid "Last month"
msgstr "上月"

#: dashboard_modules/google_analytics.py:169
#: dashboard_modules/yandex_metrika.py:124
msgid "Last quarter"
msgstr "最近15分钟"

#: dashboard_modules/google_analytics.py:170
#: dashboard_modules/yandex_metrika.py:125
msgid "Last year"
msgstr "去年"

#: dashboard_modules/google_analytics.py:180
#: dashboard_modules/yandex_metrika.py:135
msgid "none"
msgstr "无"

#: dashboard_modules/google_analytics.py:183
#: dashboard_modules/yandex_metrika.py:138
msgid "grant access first"
msgstr "优先分配访问权"

#: dashboard_modules/google_analytics.py:183
#: dashboard_modules/yandex_metrika.py:138
msgid "counters loading failed"
msgstr "统计信息加载失败"

#: dashboard_modules/google_analytics.py:188
#: dashboard_modules/yandex_metrika.py:143
msgid "Show"
msgstr "显示"

#: dashboard_modules/google_analytics.py:189
#: dashboard_modules/google_analytics.py:326
#: templates/jet.dashboard/modules/google_analytics_period_visitors.html:15
msgid "users"
msgstr "用户"

#: dashboard_modules/google_analytics.py:190
#: dashboard_modules/google_analytics.py:327
#: templates/jet.dashboard/modules/google_analytics_period_visitors.html:16
msgid "sessions"
msgstr "用户会话"

#: dashboard_modules/google_analytics.py:191
#: dashboard_modules/google_analytics.py:328
#: dashboard_modules/yandex_metrika.py:146
#: dashboard_modules/yandex_metrika.py:267
#: templates/jet.dashboard/modules/google_analytics_period_visitors.html:17
#: templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:17
msgid "views"
msgstr "视图"

#: dashboard_modules/google_analytics.py:193
#: dashboard_modules/google_analytics.py:201
#: dashboard_modules/yandex_metrika.py:148
#: dashboard_modules/yandex_metrika.py:156
msgid "Group"
msgstr "组"

#: dashboard_modules/google_analytics.py:194
#: dashboard_modules/google_analytics.py:202
#: dashboard_modules/yandex_metrika.py:149
#: dashboard_modules/yandex_metrika.py:157
msgid "By day"
msgstr "按天"

#: dashboard_modules/google_analytics.py:195
#: dashboard_modules/google_analytics.py:203
#: dashboard_modules/yandex_metrika.py:150
#: dashboard_modules/yandex_metrika.py:158
msgid "By week"
msgstr "按周"

#: dashboard_modules/google_analytics.py:196
#: dashboard_modules/google_analytics.py:204
#: dashboard_modules/yandex_metrika.py:151
#: dashboard_modules/yandex_metrika.py:159
msgid "By month"
msgstr "按月"

#: dashboard_modules/google_analytics.py:277
#, python-format
msgid ""
"Please <a href=\"%s\">attach Google account and choose Google Analytics "
"counter</a> to start using widget"
msgstr ""

#: dashboard_modules/google_analytics.py:280
#, python-format
msgid ""
"Please <a href=\"%s\">select Google Analytics counter</a> to start using "
"widget"
msgstr ""

#: dashboard_modules/google_analytics.py:299
#: dashboard_modules/google_analytics_views.py:42
#: dashboard_modules/yandex_metrika.py:236
#: dashboard_modules/yandex_metrika_views.py:37
msgid "API request failed."
msgstr "API请求失败"

#: dashboard_modules/google_analytics.py:301
#: dashboard_modules/yandex_metrika.py:238
#, python-format
msgid " Try to <a href=\"%s\">revoke and grant access</a> again"
msgstr ""

#: dashboard_modules/google_analytics.py:311
msgid "Google Analytics visitors totals"
msgstr ""

#: dashboard_modules/google_analytics.py:330
#: dashboard_modules/google_analytics.py:388
#: dashboard_modules/google_analytics.py:438
#: dashboard_modules/yandex_metrika.py:269
#: dashboard_modules/yandex_metrika.py:321
#: dashboard_modules/yandex_metrika.py:365
msgid "Bad server response"
msgstr "服务器响应异常"

#: dashboard_modules/google_analytics.py:340
msgid "Google Analytics visitors chart"
msgstr ""

#: dashboard_modules/google_analytics.py:398
msgid "Google Analytics period visitors"
msgstr ""

#: dashboard_modules/google_analytics_views.py:26
#: dashboard_modules/google_analytics_views.py:46
#: dashboard_modules/yandex_metrika_views.py:23
#: dashboard_modules/yandex_metrika_views.py:45
msgid "Module not found"
msgstr "未找到模块"

#: dashboard_modules/google_analytics_views.py:44
#: dashboard_modules/yandex_metrika_views.py:43
msgid "Bad arguments"
msgstr "参数错误"

#: dashboard_modules/yandex_metrika.py:144
#: dashboard_modules/yandex_metrika.py:265
#: templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:15
msgid "visitors"
msgstr "访问者"

#: dashboard_modules/yandex_metrika.py:145
#: dashboard_modules/yandex_metrika.py:266
#: templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:16
msgid "visits"
msgstr ""

#: dashboard_modules/yandex_metrika.py:219
#, python-format
msgid ""
"Please <a href=\"%s\">attach Yandex account and choose Yandex Metrika "
"counter</a> to start using widget"
msgstr ""

#: dashboard_modules/yandex_metrika.py:222
#, python-format
msgid ""
"Please <a href=\"%s\">select Yandex Metrika counter</a> to start using widget"
msgstr ""

#: dashboard_modules/yandex_metrika.py:250
msgid "Yandex Metrika visitors totals"
msgstr ""

#: dashboard_modules/yandex_metrika.py:279
msgid "Yandex Metrika visitors chart"
msgstr ""

#: dashboard_modules/yandex_metrika.py:331
msgid "Yandex Metrika period visitors"
msgstr ""

#: models.py:11 modules.py:164
msgid "Title"
msgstr "标题"

#: models.py:12
msgid "module"
msgstr "模块"

#: models.py:13
msgid "application name"
msgstr "应用名称"

#: models.py:14
msgid "user"
msgstr "用户"

#: models.py:15
msgid "column"
msgstr "列"

#: models.py:16
msgid "order"
msgstr "排序"

#: models.py:17
msgid "settings"
msgstr "设置"

#: models.py:18
msgid "children"
msgstr "子项"

#: models.py:19
msgid "collapsed"
msgstr "展开"

#: models.py:22
msgid "user dashboard module"
msgstr "用户dashboard模块"

#: models.py:23
msgid "user dashboard modules"
msgstr "用户dashboard模块"

#: modules.py:163
msgid "URL"
msgstr "URL"

#: modules.py:165
msgid "External link"
msgstr "外部链接"

#: modules.py:169
msgid "Layout"
msgstr "布局"

#: modules.py:169
msgid "Stacked"
msgstr ""

#: modules.py:169
msgid "Inline"
msgstr ""

#: modules.py:215 modules.py:239
msgid "Links"
msgstr "链接"

#: modules.py:238
msgid "Link"
msgstr "链接"

#: modules.py:372
msgid "Models"
msgstr ""

#: modules.py:416 modules.py:523
msgid "Items limit"
msgstr ""

#: modules.py:524
msgid "Feed URL"
msgstr ""

#: modules.py:555
msgid "RSS Feed"
msgstr ""

#: modules.py:601
msgid "You must install the FeedParser python module"
msgstr ""

#: modules.py:606
msgid "You must provide a valid feed URL"
msgstr ""

#: templates/jet.dashboard/dashboard.html:17
msgid "Delete widget"
msgstr "删除widget"

#: templates/jet.dashboard/dashboard.html:18
msgid "Are you sure want to delete this widget?"
msgstr "你确定要删除这个widget吗？"

#: templates/jet.dashboard/dashboard_tools.html:12
msgid "widgets"
msgstr ""

#: templates/jet.dashboard/dashboard_tools.html:13
msgid "available"
msgstr "可用的"

#: templates/jet.dashboard/dashboard_tools.html:18
msgid "initials"
msgstr ""

#: templates/jet.dashboard/dashboard_tools.html:23
#: templates/jet.dashboard/modules/app_list.html:18
#: templates/jet.dashboard/modules/model_list.html:8
msgid "Add"
msgstr "添加"

#: templates/jet.dashboard/dashboard_tools.html:26
#: templates/jet.dashboard/dashboard_tools.html:32
msgid "Reset widgets"
msgstr "重置widgets"

#: templates/jet.dashboard/dashboard_tools.html:33
msgid "Are you sure want to reset widgets?"
msgstr "你确定要重置widgets吗?"

#: templates/jet.dashboard/module.html:9
#: templates/jet.dashboard/modules/app_list.html:24
#: templates/jet.dashboard/modules/model_list.html:14 views.py:90
msgid "Change"
msgstr "修改"

#: templates/jet.dashboard/module.html:12
#: templates/jet.dashboard/update_module.html:55
#: templates/jet.dashboard/update_module.html:57
msgid "Delete"
msgstr "删除"

#: templates/jet.dashboard/modules/app_list.html:7
#: templates/jet.dashboard/modules/app_list.html:10
#, python-format
msgid "Models in the %(name)s application"
msgstr "在%(name)s应用下的Models"

#: templates/jet.dashboard/modules/feed.html:13
#: templates/jet.dashboard/modules/google_analytics_period_visitors.html:34
#: templates/jet.dashboard/modules/google_analytics_visitors_chart.html:30
#: templates/jet.dashboard/modules/google_analytics_visitors_totals.html:23
#: templates/jet.dashboard/modules/link_list.html:26
#: templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:34
#: templates/jet.dashboard/modules/yandex_metrika_visitors_chart.html:30
#: templates/jet.dashboard/modules/yandex_metrika_visitors_totals.html:23
msgid "Nothing to show"
msgstr "没有内容"

#: templates/jet.dashboard/modules/google_analytics_period_visitors.html:14
#: templates/jet.dashboard/modules/yandex_metrika_period_visitors.html:14
msgid "Date"
msgstr "日期"

#: templates/jet.dashboard/modules/recent_actions.html:6
msgid "None available"
msgstr "没有可用内容"

#: templates/jet.dashboard/modules/recent_actions.html:30
msgid "Unknown content"
msgstr "未知内容"

#: templates/jet.dashboard/update_module.html:7
msgid "Home"
msgstr "首页"

#: templates/jet.dashboard/update_module.html:25
msgid "Please correct the errors below."
msgstr "请修正以下错误"

#: templates/jet.dashboard/update_module.html:69
#, python-format
msgid "Add another %(verbose_name)s"
msgstr "新增%(verbose_name)s"

#: templates/jet.dashboard/update_module.html:81
msgid "Save"
msgstr "保存"

#: views.py:19
msgid "Widget was successfully updated"
msgstr "Widget成功更新"

#: views.py:94 views.py:95
msgid "Items"
msgstr ""

#: views.py:161
msgid "Widget has been successfully added"
msgstr "Widget成功添加"
