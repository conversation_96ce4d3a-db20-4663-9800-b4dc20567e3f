{
    "info": {
        "_postman_id": "a9a72c3b-8a6e-4b9a-9b9a-0e2c6b6d3b3a",
        "name": "NEM API",
        "description": "Postman collection for the NEM API, including both HTTP and WebSocket endpoints.",
        "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
    },
    "item": [
        {
            "name": "Authentication",
            "item": [
                {
                    "name": "User Registration",
                    "request": {
                        "method": "POST",
                        "header": [],
                        "body": {
                            "mode": "raw",
                            "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"first_name\": \"John\",\n    \"last_name\": \"Doe\"\n}",
                            "options": {
                                "raw": {
                                    "language": "json"
                                }
                            }
                        },
                        "url": {
                            "raw": "{{base_url}}/api/auth/registration/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "auth",
                                "registration",
                                ""
                            ]
                        },
                        "description": "Register a new user."
                    },
                    "response": []
                },
                {
                    "name": "User Login",
                    "request": {
                        "method": "POST",
                        "header": [],
                        "body": {
                            "mode": "raw",
                            "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}",
                            "options": {
                                "raw": {
                                    "language": "json"
                                }
                            }
                        },
                        "url": {
                            "raw": "{{base_url}}/api/auth/login/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "auth",
                                "login",
                                ""
                            ]
                        },
                        "description": "Login a user and get an access token."
                    },
                    "response": []
                },
                {
                    "name": "Logout",
                    "request": {
                        "method": "POST",
                        "header": [
                            {
                                "key": "Authorization",
                                "value": "Bearer {{access_token}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{base_url}}/api/auth/logout/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "auth",
                                "logout",
                                ""
                            ]
                        },
                        "description": "Logout a user."
                    },
                    "response": []
                },
                {
                    "name": "Deactivate Account",
                    "request": {
                        "method": "POST",
                        "header": [
                            {
                                "key": "Authorization",
                                "value": "Bearer {{access_token}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{base_url}}/api/auth/deactivate-account/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "auth",
                                "deactivate-account",
                                ""
                            ]
                        },
                        "description": "Deactivate a user's account."
                    },
                    "response": []
                },
                {
                    "name": "Request Account Reactivation",
                    "request": {
                        "method": "POST",
                        "header": [],
                        "body": {
                            "mode": "raw",
                            "raw": "{\n    \"email\": \"<EMAIL>\"\n}",
                            "options": {
                                "raw": {
                                    "language": "json"
                                }
                            }
                        },
                        "url": {
                            "raw": "{{base_url}}/api/auth/request-account-reactivation/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "auth",
                                "request-account-reactivation",
                                ""
                            ]
                        },
                        "description": "Request to reactivate a user's account."
                    },
                    "response": []
                },
                {
                    "name": "Reactivate Account",
                    "request": {
                        "method": "GET",
                        "header": [],
                        "url": {
                            "raw": "{{base_url}}/api/auth/reactivate-account/<uidb64>/<token>/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "auth",
                                "reactivate-account",
                                "<uidb64>",
                                "<token>",
                                ""
                            ]
                        },
                        "description": "Reactivate a user's account."
                    },
                    "response": []
                },
                {
                    "name": "Get Token",
                    "request": {
                        "method": "POST",
                        "header": [],
                        "body": {
                            "mode": "raw",
                            "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}",
                            "options": {
                                "raw": {
                                    "language": "json"
                                }
                            }
                        },
                        "url": {
                            "raw": "{{base_url}}/api/auth/token/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "auth",
                                "token",
                                ""
                            ]
                        },
                        "description": "Get a new access token."
                    },
                    "response": []
                },
                {
                    "name": "Refresh Token",
                    "request": {
                        "method": "POST",
                        "header": [],
                        "body": {
                            "mode": "raw",
                            "raw": "{\n    \"refresh\": \"{{refresh_token}}\"\n}",
                            "options": {
                                "raw": {
                                    "language": "json"
                                }
                            }
                        },
                        "url": {
                            "raw": "{{base_url}}/api/auth/refresh-token/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "auth",
                                "refresh-token",
                                ""
                            ]
                        },
                        "description": "Refresh an access token."
                    },
                    "response": []
                },
                {
                    "name": "Verify Token",
                    "request": {
                        "method": "POST",
                        "header": [],
                        "body": {
                            "mode": "raw",
                            "raw": "{\n    \"token\": \"{{access_token}}\"\n}",
                            "options": {
                                "raw": {
                                    "language": "json"
                                }
                            }
                        },
                        "url": {
                            "raw": "{{base_url}}/api/auth/verify-token/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "auth",
                                "verify-token",
                                ""
                            ]
                        },
                        "description": "Verify an access token."
                    },
                    "response": []
                },
                {
                    "name": "Resend Verification Email",
                    "request": {
                        "method": "POST",
                        "header": [],
                        "body": {
                            "mode": "raw",
                            "raw": "{\n    \"email\": \"<EMAIL>\"\n}",
                            "options": {
                                "raw": {
                                    "language": "json"
                                }
                            }
                        },
                        "url": {
                            "raw": "{{base_url}}/api/auth/resend_verification_email/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "auth",
                                "resend_verification_email",
                                ""
                            ]
                        },
                        "description": "Resend the verification email."
                    },
                    "response": []
                },
                {
                    "name": "Verify Account",
                    "request": {
                        "method": "GET",
                        "header": [],
                        "url": {
                            "raw": "{{base_url}}/api/auth/verify-account/<uidb64>/<token>/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "auth",
                                "verify-account",
                                "<uidb64>",
                                "<token>",
                                ""
                            ]
                        },
                        "description": "Verify a user's account."
                    },
                    "response": []
                },
                {
                    "name": "Change Password",
                    "request": {
                        "method": "POST",
                        "header": [
                            {
                                "key": "Authorization",
                                "value": "Bearer {{access_token}}",
                                "type": "text"
                            }
                        ],
                        "body": {
                            "mode": "raw",
                            "raw": "{\n    \"old_password\": \"password123\",\n    \"new_password\": \"newpassword123\"\n}",
                            "options": {
                                "raw": {
                                    "language": "json"
                                }
                            }
                        },
                        "url": {
                            "raw": "{{base_url}}/api/auth/change-password/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "auth",
                                "change-password",
                                ""
                            ]
                        },
                        "description": "Change a user's password."
                    },
                    "response": []
                },
                {
                    "name": "Reset Password",
                    "request": {
                        "method": "POST",
                        "header": [],
                        "body": {
                            "mode": "raw",
                            "raw": "{\n    \"email\": \"<EMAIL>\"\n}",
                            "options": {
                                "raw": {
                                    "language": "json"
                                }
                            }
                        },
                        "url": {
                            "raw": "{{base_url}}/api/auth/reset-password/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "auth",
                                "reset-password",
                                ""
                            ]
                        },
                        "description": "Request a password reset."
                    },
                    "response": []
                },
                {
                    "name": "Confirm Reset Password",
                    "request": {
                        "method": "POST",
                        "header": [],
                        "body": {
                            "mode": "raw",
                            "raw": "{\n    \"new_password\": \"newpassword123\"\n}",
                            "options": {
                                "raw": {
                                    "language": "json"
                                }
                            }
                        },
                        "url": {
                            "raw": "{{base_url}}/api/auth/confirm-reset-password/<uidb64>/<token>/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "auth",
                                "confirm-reset-password",
                                "<uidb64>",
                                "<token>",
                                ""
                            ]
                        },\n                        "description": "Confirm a password reset."
                    },
                    "response": []
                }
            ]
        },
        {
            "name": "API",
            "item": [
                {
                    "name": "Current User Profile",
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Authorization",
                                "value": "Bearer {{access_token}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{base_url}}/api/current-user/",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "current-user",
                                ""
                            ]
                        },
                        "description": "Get the current user's profile."
                    },
                    "response": []
                },
                {
                    "name": "Search",
                    "request": {
                        "method": "GET",
                        "header": [],
                        "url": {
                            "raw": "{{base_url}}/api/search/?q=test",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "search",
                                ""
                            ],
                            "query": [
                                {
                                    "key": "q",
                                    "value": "test"
                                }
                            ]
                        },
                        "description": "Search for companies, products, services, and posts."
                    },
                    "response": []
                },
                {
                    "name": "Companies",
                    "item": [
                        {
                            "name": "List Companies",
                            "request": {
                                "method": "GET",
                                "header": [],
                                "url": {
                                    "raw": "{{base_url}}/api/companies/",
                                    "host": [
                                        "{{base_url}}"
                                    ],
                                    "path": [
                                        "api",
                                        "companies",
                                        ""
                                    ]
                                }
                            },
                            "response": []
                        },
                        {
                            "name": "Create Company",
                            "request": {
                                "method": "POST",
                                "header": [
                                    {
                                        "key": "Authorization",
                                        "value": "Bearer {{access_token}}",
                                        "type": "text"
                                    }
                                ],
                                "body": {
                                    "mode": "raw",
                                    "raw": "{\n    \"name\": \"New Company\"\n}",
                                    "options": {
                                        "raw": {
                                            "language": "json"
                                        }
                                    }
                                },
                                "url": {
                                    "raw": "{{base_url}}/api/companies/",
                                    "host": [
                                        "{{base_url}}"
                                    ],
                                    "path": [
                                        "api",
                                        "companies",
                                        ""
                                    ]
                                }
                            },
                            "response": []
                        },
                        {
                            "name": "Get Company",
                            "request": {
                                "method": "GET",
                                "header": [],
                                "url": {
                                    "raw": "{{base_url}}/api/companies/:id/",
                                    "host": [
                                        "{{base_url}}"
                                    ],
                                    "path": [
                                        "api",
                                        "companies",
                                        ":id",
                                        ""
                                    ],
                                    "variable": [
                                        {
                                            "key": "id"
                                        }
                                    ]
                                }
                            },
                            "response": []
                        },
                        {
                            "name": "Update Company",
                            "request": {
                                "method": "PUT",
                                "header": [
                                    {
                                        "key": "Authorization",
                                        "value": "Bearer {{access_token}}",
                                        "type": "text"
                                    }
                                ],
                                "body": {
                                    "mode": "raw",
                                    "raw": "{\n    \"name\": \"Updated Company\"\n}",
                                    "options": {
                                        "raw": {
                                            "language": "json"
                                        }
                                    }
                                },
                                "url": {
                                    "raw": "{{base_url}}/api/companies/:id/",
                                    "host": [
                                        "{{base_url}}"
                                    ],
                                    "path": [
                                        "api",
                                        "companies",
                                        ":id",
                                        ""
                                    ],
                                    "variable": [
                                        {
                                            "key": "id"
                                        }
                                    ]
                                }
                            },
                            "response": []
                        },
                        {
                            "name": "Partial Update Company",
                            "request": {
                                "method": "PATCH",
                                "header": [
                                    {
                                        "key": "Authorization",
                                        "value": "Bearer {{access_token}}",
                                        "type": "text"
                                    }
                                ],
                                "body": {
                                    "mode": "raw",
                                    "raw": "{\n    \"name\": \"Partial Updated Company\"\n}",
                                    "options": {
                                        "raw": {
                                            "language": "json"
                                        }
                                    }
                                },
                                "url": {
                                    "raw": "{{base_url}}/api/companies/:id/",
                                    "host": [
                                        "{{base_url}}"
                                    ],
                                    "path": [
                                        "api",
                                        "companies",
                                        ":id",
                                        ""
                                    ],
                                    "variable": [
                                        {
                                            "key": "id"
                                        }
                                    ]
                                }
                            },
                            "response": []
                        },
                        {
                            "name": "Delete Company",
                            "request": {
                                "method": "DELETE",
                                "header": [
                                    {
                                        "key": "Authorization",
                                        "value": "Bearer {{access_token}}",
                                        "type": "text"
                                    }
                                ],
                                "url": {
                                    "raw": "{{base_url}}/api/companies/:id/",
                                    "host": [
                                        "{{base_url}}"
                                    ],
                                    "path": [
                                        "api",
                                        "companies",
                                        ":id",
                                        ""
                                    ],
                                    "variable": [
                                        {
                                            "key": "id"
                                        }
                                    ]
                                }
                            },
                            "response": []
                        }
                    ]
                }
            ]
        },
        {
            "name": "WebSockets",
            "item": [
                {
                    "name": "All Chats",
                    "request": {
                        "method": "GET",
                        "header": [],
                        "url": {
                            "raw": "{{ws_base_url}}/ws/chat/",
                            "protocol": "ws",
                            "host": [
                                "{{ws_base_url}}"
                            ],
                            "path": [
                                "ws",
                                "chat",
                                ""
                            ]
                        },
                        "description": "WebSocket for all chats."
                    },
                    "response": []
                },
                {
                    "name": "Chat",
                    "request": {
                        "method": "GET",
                        "header": [],
                        "url": {
                            "raw": "{{ws_base_url}}/ws/chat/<room_name>/",
                            "protocol": "ws",
                            "host": [
                                "{{ws_base_url}}"
                            ],
                            "path": [
                                "ws",
                                "chat",
                                "<room_name>",
                                ""
                            ]
                        },
                        "description": "WebSocket for a specific chat room."
                    },
                    "response": []
                },
                {
                    "name": "Group Chat",
                    "request": {
                        "method": "GET",
                        "header": [],
                        "url": {
                            "raw": "{{ws_base_url}}/ws/group/<room_name>/",
                            "protocol": "ws",
                            "host": [
                                "{{ws_base_url}}"
                            ],
                            "path": [
                                "ws",
                                "group",
                                "<room_name>",
                                ""
                            ]
                        },
                        "description": "WebSocket for a specific group chat room."
                    },
                    "response": []
                },
                {
                    "name": "App Socket",
                    "request": {
                        "method": "GET",
                        "header": [],
                        "url": {
                            "raw": "{{ws_base_url}}/ws/app/",
                            "protocol": "ws",
                            "host": [
                                "{{ws_base_url}}"
                            ],
                            "path": [
                                "ws",
                                "app",
                                ""
                            ]
                        },
                        "description": "General app WebSocket."
                    },
                    "response": []
                },
                {
                    "name": "Notifications",
                    "request": {
                        "method": "GET",
                        "header": [],
                        "url": {
                            "raw": "{{ws_base_url}}/ws/notifications/?token={{access_token}}",
                            "protocol": "ws",
                            "host": [
                                "{{ws_base_url}}"
                            ],
                            "path": [
                                "ws",
                                "notifications",
                                ""
                            ],
                            "query": [
                                {
                                    "key": "token",
                                    "value": "{{access_token}}"
                                }
                            ]
                        },
                        "description": "WebSocket for notifications."
                    },
                    "response": []
                }
            ]
        }
    ],
    "variable": [
        {
            "key": "base_url",
            "value": "http://127.0.0.1:8000"
        },
        {
            "key": "ws_base_url",
            "value": "ws://127.0.0.1:8000"
        },
        {
            "key": "access_token",
            "value": ""
        },
        {
            "key": "refresh_token",
            "value": ""
        }
    ]
}