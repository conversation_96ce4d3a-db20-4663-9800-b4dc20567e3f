import jwt
from django.conf import settings
from django.contrib.auth import get_user_model
from channels.middleware import BaseMiddleware
from channels.db import database_sync_to_async
from urllib.parse import parse_qs
from django.core.exceptions import ValidationError
from core.responselib.helpers import apiResponse

class JWTAuthMiddleware(BaseMiddleware):
    """Custom JWT authentication middleware for Django Channels"""

    async def __call__(self, scope, receive, send):
        from django.contrib.auth.models import AnonymousUser 
        query_string = parse_qs(scope["query_string"].decode())
        token = query_string.get("token", [None])[0] 

        if token:
            user = await self.get_user_from_token(token)
            scope["user"] = user if user else AnonymousUser()
        else:
            scope["user"] = AnonymousUser()

        return await super().__call__(scope, receive, send)

    @database_sync_to_async
    def get_user_from_token(self, token):
        """Decode JWT token and fetch the authenticated user"""
        from django.contrib.auth.models import AnonymousUser 
        User = get_user_model()
        try:
            decoded_data = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
            user_id = decoded_data.get("user_id")
            return User.objects.get(id=user_id)
        except jwt.ExpiredSignatureError:
            print("JWT token has expired")
            raise ValidationError(apiResponse(message="JWT token has expired", success=False))
        except jwt.DecodeError:
            print("JWT token is invalid") 
            raise ValidationError(apiResponse(message="JWT token is invalid", success=False))
        except User.DoesNotExist:
            print("User does not exist") 
            raise ValidationError(apiResponse(message="User does not exist", success=False))
        return AnonymousUser()
