import json
from channels.generic.websocket import AsyncWebsocketConsumer
from .clients import register_client, unregister_client
from channels.db import database_sync_to_async

class AppSocketConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        user = self.scope["user"]
        if user.is_anonymous:
            return await self.close()

        self.user_id = str(user.id)
        self.channel = self.channel_name

        register_client(self.user_id, self.channel)
        await self.accept()

        # await self.mark_user_online()

    async def disconnect(self, close_code):
        unregister_client(self.user_id, self.channel)
        await self.mark_user_offline()

    async def receive(self, text_data):
        data = json.loads(text_data)
        event_type = data.get("event")

        if event_type == "send":
            await self.handle_send(data)

    async def handle_send(self, data):
        message = data.get("message")
        await self.channel_layer.group_send(
            "chatroom",
            {
                "type": "new_message",
                "userId": self.user_id,
                "message": message,
            }
        )

    async def new_message(self, event):
        await self.send(text_data=json.dumps({
            "event": "newMessage",
            "userId": event["userId"],
            "message": event["message"],
        }))

    @database_sync_to_async
    def mark_user_online(self):
        from django.contrib.auth import get_user_model
        User = get_user_model()
        # User.objects.filter(id=self.user_id).update(is_online=True)

    @database_sync_to_async
    def mark_user_offline(self):
        from django.contrib.auth import get_user_model
        User = get_user_model()
        # User.objects.filter(id=self.user_id).update(is_online=False)
