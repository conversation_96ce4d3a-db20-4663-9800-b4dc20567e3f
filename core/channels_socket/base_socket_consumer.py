import json
from channels.generic.websocket import AsyncWebsocketConsumer

class BaseWebSocketConsumer(AsyncWebsocketConsumer):
    """A reusable base WebSocket consumer for handling connections, groups, and messages."""

    async def connect(self):
        """Handles WebSocket connection authentication."""
        if self.scope["user"].is_authenticated:
            self.user = self.scope["user"]
            self.group_name = self.get_group_name()
            
            if self.channel_layer is None:
                print("🔴 ERROR: channel_layer is None. Check ASGI settings!")
            
            await self.channel_layer.group_add(self.group_name, self.channel_name)
            await self.accept()
        else:
            await self.close()

    async def disconnect(self, code):
        """Handles WebSocket disconnection."""
        if hasattr(self, "group_name"):
            await self.channel_layer.group_discard(self.group_name, self.channel_name)
        print(f"Client disconnected with code: {code}")

    async def receive(self, text_data):
        """Handles received messages from the client."""
        data = json.loads(text_data)
        await self.handle_message(data)

    async def send_message(self, message):
        """Sends a message to the WebSocket."""
        await self.send(text_data=json.dumps(message))

    async def send_group_message(self, event):
        """Sends a message to a WebSocket group."""
        await self.send(text_data=json.dumps(event["message"]))

    async def handle_message(self, data):
        """Handles incoming messages (to be overridden by child classes)."""
        pass

    def get_group_name(self):
        """Returns the group name (to be overridden by child classes)."""
        raise NotImplementedError("Subclasses must implement `get_group_name` method.")
