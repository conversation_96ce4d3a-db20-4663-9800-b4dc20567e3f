import json
from channels.layers import get_channel_layer
from clients import get_user_channels

channel_layer = get_channel_layer()

async def send_websocket_message_to_users(user_ids, payload, event="notification"):
    for user_id in user_ids:
        for channel in get_user_channels(user_id):
            await channel_layer.send(channel, {
                "type": "emit_event",
                "event": event,
                "data": payload,
            })

# Inject this in consumers.py to handle the event
async def emit_event(self, event):
    await self.send(text_data=json.dumps({
        "event": event["event"],
        "data": event["data"],
    }))
