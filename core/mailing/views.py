from dotenv import load_dotenv
import sib_api_v3_sdk
from sib_api_v3_sdk.rest import ApiException
from django.template.loader import render_to_string
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode

from datetime import datetime
from django.conf import settings
current_year = datetime.now().year

load_dotenv()

token_generator = PasswordResetTokenGenerator()
frontend_url = settings.FRONTEND_URL
login_url = f"{frontend_url}/login?next=/co/settings"
BREVO_CONFIGURATION_API_KEY = settings.BREVO_CONFIGURATION_API_KEY


def get_user_name(user) -> str:
    return user.first_name if user.first_name else user.email.split("@")[0]


def send_email(subject, html_content, recipient_email, sender_email=None):
    # Configure the Brevo API client
    configuration = sib_api_v3_sdk.Configuration()
    configuration.api_key['api-key'] = BREVO_CONFIGURATION_API_KEY
    api_instance = sib_api_v3_sdk.TransactionalEmailsApi(sib_api_v3_sdk.ApiClient(configuration))

    # Create the email object
    send_smtp_email = sib_api_v3_sdk.SendSmtpEmail(
        sender={'email': sender_email if sender_email else settings.DEFAULT_FROM_EMAIL, 'name': 'Connectize' },
        to=[{'email': recipient_email}],
        subject=subject,
        html_content=html_content
    )

    try:
        api_response = api_instance.send_transac_email(send_smtp_email)
        print(api_response)
        return api_response
    except ApiException as e:
        print(f"An error occurred: {e}")
        return None


def send_verification_email(user, request):
    token = token_generator.make_token(user)
    uid = urlsafe_base64_encode(force_bytes(user.pk))

    verification_url = f"{frontend_url}/verify-account?token={token}&uid={uid}&email={user.email}"
    
    user_name = get_user_name(user)

    html_message = render_to_string("emails/verification_email.html", {
        "user_name": user_name,
        "verification_url": verification_url,
        "current_year": current_year,
    }) 
    
    send_email(
        subject="Account Verification",
        html_content=html_message,
        recipient_email=user.email
    )


def send_reset_password_email(user, request):
    token = token_generator.make_token(user)
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    
    reset_url = f"{frontend_url}/confirm-reset-password?token={token}&uid={uid}&email={user.email}"
    
    user_name = get_user_name(user)
    print(f"{user_name=}")

    html_message = render_to_string("emails/reset_password.html", {
        "user_name": user_name,
        "reset_url": reset_url,
        "current_year": current_year,
    })
    
    send_email(
        subject="Reset Password",
        html_content=html_message,
        recipient_email=user.email
    )


def send_representation_request_email(user, repId, token, representation_requester):
    
    company_url = f"{frontend_url}/{representation_requester}"
    representation_url = f"{frontend_url}/co/representatives/accept?token={token}&company={representation_requester}&company_url={company_url}&rep_id={repId}"
    
    user_name = get_user_name(user)

    html_message = render_to_string("emails/representation.html", {
        "user_name": user_name,
        "representation_url": representation_url,
        "representation_requester":representation_requester,
        "current_year":current_year,
        "company_url":company_url,
    })
    
    send_email(
        subject="Accept Invitation",
        html_content=html_message,
        recipient_email=user.email
    )
    
    
def send_deactivation_email_to_admin(user):
    
    user_name = get_user_name(user)

    html_message = render_to_string("emails/deactivation_email.html", {
        "user_name": user_name,
        "user_email": user.email,
    })
    html_message_user = render_to_string("emails/user_deactivation_email.html", {
        "user_name": user_name,
        "user_email": user.email, "current_year":current_year,
    })
    
    send_email(
        subject="Account Deactivation",
        html_content=html_message_user,
        recipient_email=user.email
    )
    
    send_email(
        subject="Account Deactivation",
        html_content=html_message,
        recipient_email=settings.DEFAULT_FROM_EMAIL,
        sender_email=user.email
    )


def send_reactivation_email(user):
    token = token_generator.make_token(user)
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    
    reactivation_url = f"{frontend_url}/reactivate-account?token={token}&uid={uid}&email={user.email}"
    
    user_name = get_user_name(user)

    html_message = render_to_string("emails/reactivation_email.html", {
        "user_name": user_name,
        "reactivation_url": reactivation_url,
        "current_year": current_year,
    })
    
    send_email(
        subject="Account Re-Activation",
        html_content=html_message,
        recipient_email=user.email
    )


def send_reactivation_notification_email(user):
        
    user_name = get_user_name(user)

    html_message = render_to_string("emails/reactivation_notification.html", {
        "user_name": user_name,
        "login_url": login_url,
        "current_year": current_year,
    })
    
    send_email(
        subject="Account Re-Activation",
        html_content=html_message,
        recipient_email=user.email
    )


def send_password_change_email(user):
    
    user_name = get_user_name(user)

    html_message = render_to_string("emails/change_password_email.html", {
        "user_name": user_name,
        "login_url": login_url,
        "current_year": current_year,
    })
    
    send_email(
        subject="Password Change",
        html_content=html_message,
        recipient_email=user.email
    )
