from rest_framework import serializers

from chat.models import GroupMessage, Message, MessageImage


class MessageImageSerializer(serializers.ModelSerializer):

    class Meta:
        model = MessageImage
        fields = ['id', 'image', 'preview', 'width', 'height']


class MessageSerializer(serializers.ModelSerializer):
    room_name = serializers.SerializerMethodField()
    images = serializers.SerializerMethodField()
    sender_info = serializers.SerializerMethodField()
    
    is_current_user = serializers.SerializerMethodField()

    class Meta:
        model = Message
        fields = ['id', 'sender_info', 'recipient', 'user', 'content', 'timestamp', 'read_at', 'room_name', 'images', 'audio_file', 'is_current_user']

    def get_room_name(self, obj):
        # Consistently generate the room name for each message
        return f"room_{min(obj.sender.id, obj.recipient.id)}_{max(obj.sender.id, obj.recipient.id)}"
    
    def get_images(self, obj):
        images = obj.images.all()
        return [
            {
                'id': image.id,
                'url': image.image.url,
                'preview': image.preview,
                'width': image.width,
                'height': image.height
            }
            for image in images
        ]
    
    def _get_avatar_url(self, user):
        request = self.context.get("request")
        if user.avatar and hasattr(user.avatar, "url"):
            return (
                request.build_absolute_uri(user.avatar.url)
                if request
                else user.avatar.url
            )
        return None

    def get_sender_info(self, obj):
        return self._get_user_info(obj.sender)

    # def get_recipient_info(self, obj):
    #     return self._get_user_info(obj.recipient)

    def _get_user_info(self, user):
        return {
            "id": user.id,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "avatar": self._get_avatar_url(user),
        }

    def get_is_current_user(self, obj):
        request = self.context.get("request")
        return (
            obj.sender == request.user
            if request and request.user.is_authenticated
            else False
        )
    

class GroupMessageSerializer(serializers.ModelSerializer):
    sender_name = serializers.CharField(source='sender.username', read_only=True)
    group_name = serializers.CharField(source='group.name', read_only=True)

    class Meta:
        model = GroupMessage
        fields = ['id', 'group', 'group_name', 'sender', 'sender_name', 'content', 'timestamp']


class LastMessageListSerializer(serializers.ModelSerializer):
    other_user = serializers.SerializerMethodField()
    is_read_by_other_user = serializers.SerializerMethodField()
    room_name = serializers.SerializerMethodField()
    unread_count = serializers.SerializerMethodField()

    class Meta:
        model = Message
        fields = [
            "id",
            "content",
            "timestamp",
            "read_at",
            "room_name",
            "other_user",
            "is_read_by_other_user",
            "unread_count",
        ]

    def get_other_user(self, obj):
        request_user = self.context["request"].user
        other = obj.recipient if obj.sender == request_user else obj.sender
        return {
            "id": other.id,
            "email": other.email,
            "first_name": other.first_name,
            "last_name": other.last_name,
            "role": other.role,
            "avatar": self._get_avatar_url(other),
        }

    def get_is_read_by_other_user(self, obj):
        request_user = self.context["request"].user
        if obj.sender == request_user:
            # I sent the message, so check if recipient read it
            return obj.read_at is not None
        # Someone else sent it, so it's not my concern
        return True

    def _get_avatar_url(self, user):
        request = self.context.get("request")
        if user.avatar and hasattr(user.avatar, "url"):
            return (
                request.build_absolute_uri(user.avatar.url)
                if request
                else user.avatar.url
            )
        return None

    def get_room_name(self, obj):
        # Consistently generate the room name for each message
        return f"room_{min(obj.sender.id, obj.recipient.id)}_{max(obj.sender.id, obj.recipient.id)}"

    def get_unread_count(self, obj):
        return getattr(obj, "unread_count", 0)