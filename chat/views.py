import logging

from django.db.models import Q
from django.utils.timezone import now
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.parsers import MultiPartParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from api.utils.image_processing import generate_message_image_preview
from authentication.models import User
from chat.models import GroupMessage, Message, MessageImage
from chat.serializers import (
    GroupMessageSerializer,
    LastMessageListSerializer,
    MessageImageSerializer,
    MessageSerializer,
)
from connectize.settings.base import BACKEND_MEDIA_URL
from connectize.websocket.unified_service import UnifiedWebSocketService
from notification.notification_service import NotificationService

logger = logging.getLogger(__name__)


class MessageViewSet(viewsets.ModelViewSet):
    queryset = Message.objects.all()
    serializer_class = MessageSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser ]

    def get_serializer_class(self):
        # Use lightweight serializer when returning last chats
        if self.request.query_params.get("last_chats") == "true":
            return LastMessageListSerializer
        return self.serializer_class

    def get_queryset(self):
        user = self.request.user

        room_name = self.request.query_params.get('room_name')
        other_user_id = self.request.query_params.get('other_user_id')  # For direct messages

        last_chats = (self.request.query_params.get("last_chats") == "true")  # For last chat

        if room_name:
            sender_id, recipient_id = map(int, room_name.replace('room_', '').split('_'))
            return Message.objects.filter(
                Q(sender_id=sender_id, recipient_id=recipient_id) |
                Q(sender_id=recipient_id, recipient_id=sender_id)
            ).order_by('-timestamp')

        if other_user_id:
            # For direct messages with a specific user
            return Message.objects.filter(
                Q(sender=user, recipient_id=other_user_id) |
                Q(sender_id=other_user_id, recipient=user)
            ).order_by('-timestamp')
        # Return all messages for the authenticated user

        if last_chats:
            # Get distinct room names with latest message ID for each chat involving the user
            messages = Message.objects.filter(
                Q(sender=user) | Q(recipient=user)
            ).order_by("-timestamp")

            seen_rooms = set()
            latest_messages = []

            for msg in messages:
                room = msg.room_name
                if room in seen_rooms:
                    continue
                seen_rooms.add(room)

                # Count unread messages where user is the recipient
                unread_count = (
                    Message.objects.filter(
                        read_at__isnull=True,
                        recipient=user,  # the logged-in user
                    )
                    .filter(
                        Q(sender=msg.sender, recipient=msg.recipient)
                        | Q(sender=msg.recipient, recipient=msg.sender)
                    )
                    .count()
                )

                msg.unread_count = unread_count
                latest_messages.append(msg)

            return latest_messages

        # Return all messages for the authenticated user
        return Message.objects.filter(Q(sender=user) | Q(recipient=user)).order_by("-timestamp")

    def perform_create(self, serializer):
        recipient_id = self.request.data.get("recipient")
        sender_id = self.request.data.get("sender")
        images = self.request.FILES.getlist('images')

        recipient = User.objects.get(id=recipient_id)
        sender = User.objects.get(id=sender_id)

        message = serializer.save(sender=self.request.user, recipient=recipient, user=self.request.user)

        # Save message images with preview generation and dimension extraction
        message_images = []
        for image in images:
            preview_data = None
            width = None
            height = None

            try:
                # Generate LQIP preview and extract dimensions
                image_data = generate_message_image_preview(image)

                if image_data:
                    preview_data = image_data.get('preview')
                    dimensions = image_data.get('dimensions', {})
                    width = dimensions.get('width')
                    height = dimensions.get('height')

                    logger.info(f"Successfully processed image: {image.name} ({width}x{height})")
                else:
                    logger.warning(f"Failed to process image: {image.name}")

            except Exception as e:
                logger.error(f"Error processing image {image.name}: {str(e)}")
                preview_data = None

            # Create MessageImage with preview and dimensions
            message_image = MessageImage.objects.create(
                message=message,
                image=image,
                preview=preview_data,
                width=width,
                height=height
            )
            message_images.append(message_image)

        image_serializer = MessageImageSerializer(message_images, many=True)

        ws_message = {
            "id": message.id,
            "sender": message.sender.id,
            "recipient": message.recipient.id,
            "user": self.request.user.id,
            "content": message.content,
            "timestamp": str(message.timestamp),
            "read_at": None,
            "room_name": message.room_name,
            "images": [
                {
                    "id": img["id"],
                    "url": img["image"],
                    "preview": img["preview"],
                    "width": img["width"],
                    "height": img["height"]
                }
                for img in image_serializer.data
            ],
            "audio_file": BACKEND_MEDIA_URL + str(message.audio_file) if message.audio_file else None
        }

        ws_recipient = message.recipient

        # Use unified WebSocket service for broadcasting
        UnifiedWebSocketService.broadcast_chat_message(
            message_data=ws_message,
            sender_id=message.sender.id,
            recipient_id=ws_recipient.id,
            room_name=message.room_name
        )

        # WebSocketService.send_websocket_notification(
        #     channel_group_name=f"chat_user_{message.user.id}",
        #     recipient=message.user,
        #     ws_message=ws_message,
        #     message_type="chat_message"
        # )

        if ws_recipient.id != self.request.user.id:
            NotificationService.create_notification(
                recipient=ws_recipient,
                sender=self.request.user,
                notification_type="messaging",
                message=f"You've received a message from {self.request.user.first_name or self.request.user.email}",
                link=f"/messages/?room_name={message.room_name}",
                title="Room messaging"
            )

    @action(detail=False, methods=['post'], url_path="mark-all-as-read")
    def mark_all_as_read(self, request):
        """
        Mark all messages for the authenticated user (or for a specific conversation)
        as read by updating their read_at fields and send real-time read receipts.
        """

        user = request.user
        room_name = request.data.get("room_name")
        if not room_name:
            return Response({"detail": "Missing room_name"}, status=400)

        try:
            sender_id, recipient_id = map(
                int, room_name.replace("room_", "").split("_")
            )
        except ValueError:
            return Response({"detail": "Invalid room_name format"}, status=400)

        # Find all unread messages in that room where the current user is the recipient
        unread_messages = (
            Message.objects.filter(read_at__isnull=True)
            .filter(
                Q(sender_id=sender_id, recipient_id=recipient_id)
                | Q(sender_id=recipient_id, recipient_id=sender_id)
            )
            .filter(recipient=user)
            .select_related('sender', 'recipient')
        )

        # Get unique senders who should receive read receipts
        message_senders = set()
        message_ids = []

        for message in unread_messages:
            if message.sender != user:  # Don't send read receipt to ourselves
                message_senders.add(message.sender)
                message_ids.append(message.id)

        # Mark messages as read
        updated = unread_messages.update(read_at=now())

        # Send read receipt notifications to each sender
        if message_senders and updated > 0:
            read_receipt_payload = {
                "type": "read_receipt",
                "room_name": room_name,
                "reader_id": user.id,
                "reader_name": user.first_name or user.email,
                "message_ids": message_ids,
                "read_at": str(now()),
                "conversation_partner": {
                    "id": user.id,
                    "name": user.first_name or user.email,
                    "email": user.email
                }
            }

            for sender in message_senders:
                # Send read receipt via unified service
                UnifiedWebSocketService.send_read_receipt(
                    receipt_data=read_receipt_payload,
                    recipient_id=sender.id,
                    room_name=room_name
                )

        return Response(
            {
                "detail": f"{updated} messages marked as read.",
                "read_receipts_sent": len(message_senders)
            },
            status=status.HTTP_200_OK
        )

    @action(detail=True, methods=['post'], url_path="mark-as-read")
    def mark_single_as_read(self, request, pk=None):
        """
        Mark a single message as read and send read receipt to the sender.
        """
        try:
            message = self.get_object()
        except Message.DoesNotExist:
            return Response({"detail": "Message not found"}, status=404)

        user = request.user

        # Only allow the recipient to mark the message as read
        if message.recipient != user:
            return Response(
                {"detail": "You can only mark messages sent to you as read"},
                status=403
            )

        # If already read, no need to update
        if message.read_at:
            return Response(
                {"detail": "Message already marked as read"},
                status=200
            )

        # Mark as read
        message.read_at = now()
        message.save()

        # Send read receipt to sender (if sender is not the same as reader)
        if message.sender != user:
            read_receipt_payload = {
                "type": "read_receipt",
                "room_name": message.room_name,
                "reader_id": user.id,
                "reader_name": user.first_name or user.email,
                "message_ids": [message.id],
                "read_at": str(message.read_at),
                "conversation_partner": {
                    "id": user.id,
                    "name": user.first_name or user.email,
                    "email": user.email
                }
            }

            # Send read receipt via unified service
            UnifiedWebSocketService.send_read_receipt(
                receipt_data=read_receipt_payload,
                recipient_id=message.sender.id,
                room_name=message.room_name
            )

        return Response(
            {"detail": "Message marked as read", "read_at": str(message.read_at)},
            status=status.HTTP_200_OK
        )


class GroupMessageViewSet(viewsets.ModelViewSet):
    queryset = GroupMessage.objects.all()
    serializer_class = GroupMessageSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Retrieve messages for a specific group
        user = self.request.user
        group_id = self.request.query_params.get('group_id')  # Get group ID from query parameters

        if group_id:
            # Ensure the user is a member of the group
            return GroupMessage.objects.filter(group_id=group_id, group__members=user)

        # Optionally, return all group messages for groups the user is a member of
        return GroupMessage.objects.filter(group__members=user)
