
from channels.db import database_sync_to_async
from django.utils.timezone import now

from connectize.websocket.consumer import BaseWebSocketConsumer


class AllChatsConsumer(BaseWebSocketConsumer):
    """Handles WebSocket connections for fetching all user chats."""

    def get_group_name(self):
        """Defines the WebSocket group name for all user chats."""
        return f"chat_user_{self.user.id}"

    async def handle_message(self, data):
        message = data.get("message")
        if message:
            await self.channel_layer.group_send(
                self.group_name,
                {
                    "type": "chat_message",
                    "message": message,
                },
            )
    
    async def chat_message(self, event):
        """Handles 'chat_message' events."""
        await self.send_message(event["message"], "message_received")

    async def read_receipt(self, event):
        """Handles 'read_receipt' events for real-time read status updates."""
        await self.send_message(event["message"], "read_receipt_received")


class ChatConsumer(BaseWebSocketConsumer):
    """Handles private chat WebSocket connections."""

    def get_group_name(self):
        """Defines the WebSocket group name for private chats."""
        return f"chat_{self.scope['url_route']['kwargs']['room_name']}"

    async def handle_message(self, data):
        """Handles messages received from clients."""
        message = data.get("message")
        if data.get("command") == "mark_as_read":
            await self.mark_message_as_read(data["message_id"], data["user_id"])
            
        if message:
            await self.channel_layer.group_send(
                self.group_name,
                {
                    "type": "chat_message",
                    "message": message,
                },
            )
            
    async def chat_message(self, event):
        """Handles 'chat_message' events."""
        await self.send_message(event["message"], "message_received")

    async def read_receipt(self, event):
        """Handles 'read_receipt' events for real-time read status updates."""
        await self.send_message(event["message"], "read_receipt_received")

    @database_sync_to_async
    def mark_message_as_read(self, message_id, user_id):
        """Marks a chat message as read."""
        from .models import Message
        message = Message.objects.filter(id=message_id, user_id=user_id).first()
        if message:
            message.read_at = now()
            message.save()


class GroupChatConsumer(BaseWebSocketConsumer):
    """Handles real-time group chat functionality."""

    def get_group_name(self):
        """Defines the WebSocket group name for group chats."""
        return f"chat_{self.scope['url_route']['kwargs']['room_name']}"

    async def handle_message(self, data):
        """Handles messages received from clients and sends them to the group."""
        message = data["message"]
        sender = data["sender"]

        # Broadcast message to the group
        await self.channel_layer.group_send(
            self.group_name,
            {
                'type': 'send_group_message',
                'message': {"message": message, "sender": sender},
            }
        )

    async def send_group_message(self, event):
        """Handles 'send_group_message' events."""
        await self.send_message(event["message"], "group_message_received")
