from django.db import models

from authentication.models import User


class Message(models.Model):
    sender = models.ForeignKey(User, related_name='sent_messages', on_delete=models.CASCADE)
    recipient = models.ForeignKey(User, related_name='received_messages', on_delete=models.CASCADE)
    user = models.ForeignKey(User, related_name='user_messages', on_delete=models.CASCADE, null=True, blank=True)
    content = models.TextField()
    audio_file = models.FileField(null=True, blank=True, upload_to="voice_notes/")
    timestamp = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now_add=True, blank=True, null=True)
    deleted_at = models.DateTimeField(blank=True, null=True)
    read_at = models.DateTimeField(null=True, blank=True)
    # other_file = models.FileField(null=True, blank=True, upload_to="voice_notes/")

    class Meta:
        ordering = ['-timestamp']

    @property
    def room_name(self):
        # Generate a unique, consistent room name
        return f"room_{min(self.sender.id, self.recipient.id)}_{max(self.sender.id, self.recipient.id)}"

    def __str__(self):
        return f'Message from {self.sender} to {self.recipient}'


class MessageImage(models.Model):
    message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='message_images/')
    preview = models.TextField(null=True, blank=True, help_text="Base64-encoded thumbnail for LQIP (Low Quality Image Placeholder)")
    width = models.IntegerField(null=True, blank=True, help_text="Original image width in pixels")
    height = models.IntegerField(null=True, blank=True, help_text="Original image height in pixels")
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f'Message image {self.id}'


class Group(models.Model):
    name = models.CharField(max_length=255)
    members = models.ManyToManyField(User, related_name="chat_groups")


class GroupMessage(models.Model):
    group = models.ForeignKey(Group, related_name="messages", on_delete=models.CASCADE)
    sender = models.ForeignKey(User, related_name="group_messages", on_delete=models.CASCADE)
    content = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)

    @property
    def room_name(self):
        return f"group_{self.group.id}"
    
    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"Message from {self.sender.username} in {self.group.name}"

